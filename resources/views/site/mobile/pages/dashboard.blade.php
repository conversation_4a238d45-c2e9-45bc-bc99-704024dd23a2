<!-- Start [ dashboard ] -->
<div class="cc-page-inner">
    <div class="cc-page-content">
        <div class="db-body-content">
            <div class="db-body-component">

                @if($competition)
                    <!-- start [ hero ] -->
                    <div class="cc-guess-section cc-guess-hero mlr-20">
                        <div class="cc-section-bg">
                            <picture>
                                <source srcset="{{ url('public') }}/gfx/guess-hero-bg.jpg" media="(min-width:1px)">
                                <img decoding="async" src="{{ url('public') }}/gfx/guess-hero-bg.jpg" alt="ps-hero-bg">
                            </picture>
                        </div>
                        
                        <div class="cc-hero-wrapper">
                            <div class="cc-hero-transparent-box">
                                <div class="text-a text-white">
                                    {!! fT('e_dashboard.a_participate.e_hero_description_mobile', '<p>Spiele jetzt die Mini Games, um dir Tickets für die Verlosung zu sichern.</p>
                                    <p>Es warten täglich neue Aufgaben und Preise auf dich.</p>') !!}
                                </div>
                            </div>

                            <div class="cc-pd-tagname">
                                <span class="cc-pd-tagname-box">
                                    <span class="cc-pd-tagname-box-in">
                                        <span class="cc-pd-tagname-text">{!! fT('e_dashboard.a_participate.a_tagespreise', 'Tagespreise') !!}</span>
                                    </span>
                                </span>
                            </div>

                            <a href="{{ route('site_page', ['page' => 'participate']) }}" class="cc-controller-card page-change-nav cc-adobe-track" {!! adobeTrackData('participate click', "dashboard", "1", "image") !!} data-href="participate" data-reload="yes">
                                <div class="cc-controller-gfx ratio ratio-1x1">
                                    <picture>
                                        <source srcset="{{ ccImgUrl($competition->prize_mobile_image, 'cc-main-prize-gfx.jpg') }}" media="(min-width:1px)">
                                        <img decoding="async" src="{{ ccImgUrl($competition->prize_mobile_image, 'cc-main-prize-gfx.jpg') }}" alt="{{ $competition->prize_name }}">
                                    </picture>
                                </div>

                                <div class="text-a text-center">
                                    <h2 class="h5">{!! $competition->prize_name !!}</h2>
                                    <p>{!! $competition->prize_description !!}</p>
                                    <div class="btn-wrapper">
                                        <span class="btn btn-primary"><span class="btn-text">{!! fT('e_dashboard.a_participate.c_participate_btn_text', 'Particapite') !!}</span></span>
                                    </div>
                                </div>
                            </a>

                            <div class="guess-the-game-box mlr-20">
                                <div class="text-a text-center text-white">
                                    <h2 class="h3">{!! fT('e_dashboard.a_participate.b_win_your_tickets_title', 'Erspiel dir deine Tickets!') !!}</h2>
                                </div>
                                <div class="guess-card-swiper-wrapper">
                                    <div class="swiper guess-card-swiper">
                                        <div class="swiper-wrapper">
                                            @php  $i = 2;  @endphp

                                            @foreach(moduleLinkData() as $modulePath => $moduleData)

                                                @php
                                                $isPlayed = isset($games[$modulePath]) && in_array($games[$modulePath], $game_tickets) ? true : false;
                                                @endphp

                                                <div class="swiper-slide">
                                                    <a href="{{ route('site_page', ['page' => $modulePath]) }}" class="guess-card-box page-change-nav {{ $isPlayed ? 'disabled' : '' }} cc-adobe-track" {!! adobeTrackData($moduleData['title'].' click', "dashboard", $i, "image") !!} data-href="{{ $modulePath }}">
                                                        <div class="guess-card-gfx ratio ratio-1x1 contain">
                                                            <picture>
                                                                <source srcset="{{ url('public/gfx/'.$moduleData['icon']) }}" media="(min-width:1px)">
                                                                <img decoding="async" src="{{ url('public/gfx/'.$moduleData['icon']) }}" alt="{{ $moduleData['title'] }} icon">
                                                            </picture>
                                                        </div>

                                                        <div class="text-a">
                                                            <h3 class="h5">{!! $moduleData['title'] !!}</h3>
                                                            <p>{!! $moduleData['sub_headline'] !!}</p>

                                                            @if($modulePath != 'faq')
                                                                <div class="btn-wrapper">
                                                                    <span class="btn btn-primary {{ $isPlayed ? 'disabled' : '' }}">
                                                                        <span class="btn-text">{!! fT('e_dashboard.a_participate.d_start', 'Start') !!}</span>
                                                                            <i class="cc_icon-ticket"><svg role="img"><title>ticket icon</title><use xlink:href="#cc_icon-ticket"></use></svg></i>
                                                                        <span class="btn-text">x{!! fS("tickets.".$modulePath, 1) !!}</span>
                                                                    </span>
                                                                </div>
                                                            @endif
                                                            
                                                        </div>
                                                    </a>
                                                </div>

                                                @php  $i++;  @endphp
                                            @endforeach

                                        </div>

                                        <div class="swiper-pagination guess-card-pagination"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- end [ hero ] -->
                @endif

                @if(!empty($day_winners))

                <!-- start [ winner-card ] -->
                <div class="cc-guess-section cc-winner-section">
                    <div class="winner-card-swiper-wrapper">
                        <div class="swiper winner-card-swiper">
                            
                            <div class="swiper-wrapper">

                                @foreach($day_winners as $winnerDay => $winners)

                                    @php
                                    $winner_disp_date = lang_month(date('d. F Y', strtotime($winnerDay)));
                                    @endphp
                                    
                                    <div class="swiper-slide">
                                        <div class="winner-card-wrapper">
                                            <div class="text-a text-center">
                                                <h2 class="h6">{!! fT('e_dashboard.b_winner.a_title', 'Die Gewinner vom [winner_date]',['winner_date' => $winner_disp_date ]) !!}</h2>
                                            </div>
                                            <div class="winner-card">

                                                @foreach($winners as $winner)
                                                    @if($winner['prize_type'] == "main")

                                                        <div class="winner-gfx-wrapper">
                                                            <div class="winner-gfx ratio contain">
                                                                <picture>
                                                                    <source srcset="{{ ccImgUrl($winner['prize_mobile_image'], 'cc-main-prize-gfx.jpg') }}" media="(min-width:1px)">
                                                                    <img decoding="async" src="{{ ccImgUrl($winner['prize_mobile_image'], 'cc-main-prize-gfx.jpg') }}" alt="{{ $winner['prize_name'] }}" loading="lazy">
                                                                </picture>
                                                            </div>
                                                            <div class="user-identity-box">
                                                                <div class="user-logo ratio ratio-1x1">
                                                                    <img decoding="async" src="{{ avtar_url($winner['avtar_url']) }}" alt="{{ $winner['nickname'] }}">
                                                                </div>
                                                                <h3 class="h6">{!! $winner['nickname'] !!}</h3>
                                                            </div>
                                                        </div>

                                                    @endif
                                                @endforeach

                                                <div class="text-a">
                                                    <h4 class="h4">{!! fT('e_dashboard.b_winner.b_runner_ups', 'Runner Ups:') !!}</h4>
                                                    <ol>
                                                        @foreach($winners as $winner)
                                                            @if($winner['prize_type'] != "main")
                                                                <li>{!! $winner['nickname'] !!}</li>
                                                            @endif
                                                        @endforeach
                                                    </ol>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                @endforeach

                            </div>

                            <a role="button" class="swiper-button-prev btn btn-primary btn-icon winner-card-prev cc-adobe-track" aria-label="prev slide" {!! adobeTrackData('left click', "dashboard", "7", "icon") !!}>
                                <i class="cc_icon-chevron-left">
                                    <svg role="img"><title>Chevron left icon</title><use xlink:href="#cc_icon-chevron-left"></use></svg>
                                </i>
                            </a>
                            <a role="button" class="swiper-button-next btn btn-primary btn-icon winner-card-next cc-adobe-track" aria-label="Next slide" {!! adobeTrackData('right click', "dashboard", "8", "icon") !!}>
                                <i class="cc_icon-chevron-right">
                                    <svg role="img"><title>Chevron right icon</title><use xlink:href="#cc_icon-chevron-right"></use></svg>
                                </i>
                            </a>
                        </div>
                    </div>
                </div>
                <!-- end [ winner-card ] -->

                @endif

                <!-- start [ live-stream ] -->
                <div class="cc-guess-section cc-live-stream">
                    <div class="live-stream-wrapper">
                        <div class="text-a">
                            <h2 class="h1 text-center">{!! fT('d_pages.e_thank_you.e_livestream', 'Livestream') !!}</h2>
                            {!! auto_ptag(fT('d_pages.e_thank_you.f_livestream_description', '<p>Nicht verpassen - die Gewinnerziehung und mehr finden täglich um 20:15 Uhr via Livestream statt. Schau vorbei!</p>')) !!}
                            <div class="ak-livestream-row">
                                <div class="ak-livestream-cell">
                                    <a href="{!! fT('d_pages.e_thank_you.g_twitch_link', 'https://www.twitch.tv/playstationde') !!}" class="ak-livestream-box twitch cc-adobe-track" {!! adobeTrackData('Twitch click', "dashboard", "9", "icon") !!} target="_blank" rel="noopener noreferrer">
                                        <span class="ak-livestream-box-icon">
                                            <i class="cc_icon-twitch-2"><svg role="img"><title>Twitch icon</title><use xlink:href="#cc_icon-twitch-2"></use></svg></i>
                                        </span>
                                        <span class="ak-livestream-box-text">{!! fT('d_pages.e_thank_you.g_twitch_btn_text', 'Dabei sein auf Twitch') !!}</span>
                                    </a>
                                </div>
                                <div class="ak-livestream-cell">
                                    <a href="{!! fT('d_pages.e_thank_you.h_instagram_link', 'http://instagram.com/playstationde') !!}" class="ak-livestream-box instagram cc-adobe-track" {!! adobeTrackData('Instagram click', "dashboard", "10", "icon") !!} target="_blank" rel="noopener noreferrer">
                                        <span class="ak-livestream-box-icon">
                                            <i class="cc_icon-instagram-2"><svg role="img"><title>Instagram icon</title><use xlink:href="#cc_icon-instagram-2"></use></svg></i>
                                        </span>
                                        <span class="ak-livestream-box-text">{!! fT('d_pages.e_thank_you.h_instagram_btn_text', 'Zusehen auf Instagram') !!}</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- end [ live-stream ] -->
                
                @if($day_of_play_offers->count() > 0)

                <!-- start [ days of play ] -->
                <div class="cc-guess-section cc-days-of-play">
                    <div class="text-a text-center">
                        <h2 class="h2">{!! fT('b_home.d_day_of_play.a_title', 'Days of Play Angebote') !!}</h2>
                        {!! auto_ptag(fT('b_home.d_day_of_play.b_description', '<p>Mach mit bei diesen Days of Play Aktivitäten</p>')) !!}
                    </div>
                    <div class="mlr-20">
                        <div class="winner-card-swiper-wrapper">
                            <div class="swiper days-of-play-swiper swiper-initialized swiper-horizontal swiper-ios swiper-backface-hidden">
                                <div class="swiper-wrapper">
                                    
                                    @foreach($day_of_play_offers as $product_offer)
                                        <div class="swiper-slide">
                                            <div class="winner-card">
                                                <div class="days-of-play-gfx ratio contain ratio-1x1">
                                                    <picture>
                                                        <source srcset="{{ ccImgUrl($product_offer['mobile_image'], "s3-product-gfx-1.jpg") }}" media="(min-width:1px)">
                                                        <img decoding="async" src="{{ ccImgUrl($product_offer['mobile_image'], "s3-product-gfx-1.jpg") }}" alt="{{ $product_offer['product_name'] }}" loading="lazy">
                                                    </picture>
                                                </div>

                                                <div class="text-a">
                                                    <h3 class="h4">{!! $product_offer['product_name'] !!}</h3>
                                                    @if($product_offer['sales_prize'])
                                                        <span class="day-of-play-price">{!! $product_offer['sales_prize'] !!}</span>
                                                    @endif
                                                    
                                                    <div class="btn-wrapper">
                                                        <a href="{{ $product_offer['offer_link'] }}" target="_blank" rel="noopener noreferrer" class="btn btn-primary cc-adobe-track" {!! adobeTrackData('offer click', "day of play offer", "1", "link") !!}><span class="btn-text">{!! fT('b_home.d_day_of_play.c_zum_angebot', 'Zum Angebot') !!}</span></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach

                                </div>
                                
                                <div class="swiper-pagination days-of-play-pagination"></div>

                                <a role="button" class="swiper-button-prev btn btn-primary btn-icon days-of-prev cc-adobe-track" aria-label="Previous slide" {!! adobeTrackData('left click', "day of play offer", "2", "link") !!}>
                                    <i class="cc_icon-chevron-left">
                                        <svg role="img"><title>Chevron left icon</title><use xlink:href="#cc_icon-chevron-left"></use></svg>
                                    </i>
                                </a>
                                <a role="button" class="swiper-button-next btn btn-primary btn-icon days-of-next cc-adobe-track" aria-label="Next slide" {!! adobeTrackData('right click', "day of play offer", "3", "link") !!}>
                                    <i class="cc_icon-chevron-right">
                                        <svg role="img"><title>Chevron right icon</title><use xlink:href="#cc_icon-chevron-right"></use></svg>
                                    </i>
                                </a>
                            <span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span></div>
                        </div>
                    </div>
                </div>
                <!-- end [ days of play ] -->

                @endif

            </div>
        </div>
    </div>
</div>
<!-- End   [ dashboard ] -->