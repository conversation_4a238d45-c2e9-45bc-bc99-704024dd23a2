<a href="#" class="btn btn-primary btn-icon offcanvas-btn-close cc-adobe-track" {!! adobeTrackData('close click', "participate", "1", "icon") !!} data-bs-dismiss="offcanvas" aria-label="Close"><i class="cc_icon-close"><svg role="img"><title>Close icon</title><use xlink:href="#cc_icon-close"></use></svg></i></a>
<div class="offcanvas-body" id="cc-participateLabel">
    <div class="cc-offcanvas-body">

        <div id="participate-form-tab">
            <div class="cc-pd-tagname">
                <span class="cc-pd-tagname-box">
                    <span class="cc-pd-tagname-box-in">
                        <span class="cc-pd-tagname-text">{!! fT('d_pages.f_participate_form.a_title', 'Tagespreise') !!}</span>
                    </span>
                </span>
            </div>
    
            <div class="row align-items-center">
                <div class="col-6">
                    <div class="cc-participate-thumb ratio ratio-1x1">
                        <img decoding="async" loading="lazy" src="{{ ccImgUrl($competition->prize_image, 'cc-main-prize-gfx.jpg') }}" alt="{{ $competition->prize_name }}">
                    </div>
                </div>
                <div class="col-6">
                    <div class="text-a cc-participate-content">
                        <h2 class="h3">{!! $competition->prize_name !!}</h2>
                        {!! auto_ptag($competition->prize_description) !!}
                    </div>

                    @if($psnUser->tickets > 0)

                        <form name="buy-ticket" class="cc-buy-ticket-form buy-ticket cc-form-a" novalidate action="#" method="post">
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label class="form-label visually-hidden" for="cc-nickname">Nickname</label>
                                        <div class="form-control-wrapper">
                                            <input type="text" name="nickname" class="form-control border-primary" placeholder="{!! fT('d_pages.f_participate_form.b_nickname', 'Nickname') !!}" id="cc-nickname" autocomplete="off" value="{{ $psnUser->nickname }}" {{ $psnUser->nickname && $psnUser->tmp_nickname != 1 ? 'readonly' : '' }} maxlength="16">
                                            <span class="invalid-feedback no-nickname">{!! fT('d_pages.f_participate_form.c_nickname_error', 'Please enter nickname') !!}</span>
                                            <span class="invalid-feedback dup-nickname d-none">{!! fT('d_pages.f_participate_form.d_nickname_already_error', 'This nickname is already in use') !!}</span>
                                        </div>
                                    </div>
                                </div>
        
                                <div class="col-12">
                                    <div class="form-group">
                                        <label class="form-label visually-hidden" for="cc-email">Your E-mail Address</label>
                                        <div class="form-control-wrapper">
                                            <input type="email" name="email" class="form-control border-primary" placeholder="{!! fT('d_pages.f_participate_form.e_email', 'Email') !!}" autocomplete="email" id="cc-email" value="{{ $psnUser->email }}" {{ $psnUser->email ? 'readonly' : '' }}>
                                            <span class="invalid-feedback no-email">{!! fT('d_pages.f_participate_form.f_email_error', 'Please enter correct email address') !!}</span>
                                            <span class="invalid-feedback dup-email d-none">{!! fT('d_pages.f_participate_form.g_email_already_error', 'This email address is already in use') !!}</span>
                                        </div>
                                    </div>
                                </div>
        
                                <div class="col-12">
                                    <div class="cc-tc-row">
                                        <div class="cc-tc-left">
                                            <div class="form-group cc-ticket-val-row">
                                                <div class="form-control-wrapper">
                                                    <input type="number" name="num_ticktes" id="cc-tickets" class="form-control form-control-label" min="1" data-max="{{ $psnUser->tickets }}">
                                                    <label class="form-label" for="cc-tickets">{!! fT('d_pages.f_participate_form.h_tickets', '# Tickets') !!}</label>
                                                    <p class="invalid-feedback no-tickets">{!! fT('d_pages.f_participate_form.i_tickets_error', 'Please enter number of tickets') !!}</p>
                                                    <p class="invalid-feedback no-enough-tickets">{!! fT('d_pages.f_participate_form.j_enough_tickets_error', "You don't have enough tickets") !!}</p>
                                                </div>
                                            </div>
                                        </div>
        
                                        <div class="cc-tc-right">
                                            <span class="cc-tc-icon ratio ratio-1x1">
                                                <img decoding="async" loading="lazy" src="{{ url('public') }}/gfx/cc-ticket-gold.svg" alt="image of gold ticket">
                                            </span>
                                        </div>
                                    </div>
        
                                    <div class="form-group">
                                        <label for="cc-bt-checkbox" class="cc-toggle-a">
                                            <input type="checkbox" value="Y" name="terms" id="cc-bt-checkbox" class="form-control">
                                            <span class="cc-toggle-a-ui">
                                                <i class="cc_icon-check"><svg role="img"><title>Check icon</title><use xlink:href="#cc_icon-check"></use></svg></i>
                                            </span>
                                            <span class="cc-toggle-a-text">
                                                {!! fT('d_pages.f_participate_form.k_accept_text', 'Yes, I accept the <a href="[terms_pdf]" target="_blank" rel="noopener noreferrer">Terms & Conditions</a>', ['terms_pdf' => ccImgUrl($competition->terms_pdf, 'sample.pdf')] ) !!}
                                            </span>
                                        </label>
                                    </div>
        
                                    <div class="form-error"></div>
        
                                    <div class="btn-spacer justify-content-end">
                                        <button type="submit" name="btn-submit" class="btn btn-primary cc-adobe-track" {!! adobeTrackData('submit click', "participate", "2", "button") !!}><span class="btn-text">{!! fT('d_pages.f_participate_form.l_participate_btn_text', 'Participate') !!}</span></button>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" name="token" >
                        </form>

                    @else

                        <div class="text-a">
                            <p>{!!  fT("d_pages.f_participate_form.m_no_ticket_msg", "You don't have any tickets to particiapte in the competition! Please earn tickets from the games and come back later!")  !!}</p>
                        </div>

                        <div class="btn-spacer mt-4 justify-content-center">
                            <a class="btn btn-primary cc-adobe-track" data-bs-dismiss="offcanvas" {!! adobeTrackData('To dashboard', "participate", "3", "button") !!}><span class="btn-text">{!! fT('d_pages.f_participate_form.n_dashboard_btn_label', 'Back To Dashboard') !!}</span></a>
                        </div>

                    @endif

                </div>
            </div>
        </div>

        <div class="d-none" id="participate-thank-you-tab">
            
        </div>
    </div>
</div>