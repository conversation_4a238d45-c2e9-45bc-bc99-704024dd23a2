<!doctype html>
<html lang="de">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <meta name="viewport" content="width=1920, initial-scale=1, shrink-to-fit=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#242424">

    <link rel="shortcut icon" type="image/x-icon" href="{{ url('public') }}/gfx/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url('public') }}/gfx/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url('public') }}/gfx/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url('public') }}/gfx/favicon-16x16.png">
    <link rel="manifest" href="{{ url('public') }}/gfx/site.webmanifest">

    <link rel="stylesheet" href="{{ url('public') }}/css/font-all.css">
    
    <style>
    .visually-hidden,.visually-hidden-focusable:not(:focus):not(:focus-within) { width: 1px!important; height: 1px!important; padding: 0!important; margin: -1px!important; overflow: hidden!important; clip: rect(0,0,0,0)!important; white-space: nowrap!important; border: 0!important;}
    .visually-hidden-focusable:not(:focus):not(:focus-within):not(caption),.visually-hidden:not(caption) { position: absolute!important; }
    .d-none { display: none !important; }
    input[type="text"], input[type="email"], input[type="password"],input[type="number"], input[type="tel"], input[type="button"],input[type="reset"],input[type="submit"], button{-webkit-font-smoothing:antialiased;-moz-font-smoothing:antialiased; font-family: "sst", Arial,sans-serif; font-weight: 400; -webkit-appearance: none; }
    html { font-size: 10px; }
    body { font-size: 1.6rem; font-weight: 400; line-height: 1.5; color: #171D2E; position: relative; font-family: "sst", Arial,sans-serif; margin:0;padding:0; background-color: #DDE0E8; overflow: hidden; }

    html,
    body { position: relative; padding: 0; margin: 0; width: 1920px; height: 1080px; overflow: hidden; }

    *, *:before, *:after { -webkit-box-sizing: border-box;box-sizing: border-box; }

    a { color: #fff; font-weight: normal; cursor: pointer; -webkit-transition: color 0.15s linear; transition: color 0.15s linear; text-decoration: underline; outline: none; }
    a:hover { color: #fff; text-decoration: none; }
    a:not(:hover):focus { outline: thin dotted; outline-offset: -1px; }
    a.btn,
    a.btn:hover { text-decoration: none; }


    .cc-winner-draw { position: relative; z-index: 0; width: 1920px; height: 1080px; background: -webkit-gradient(linear, left top, right top, from(rgb(114, 106, 253)), to(rgb(9, 175, 251))); background: linear-gradient(90deg, rgb(114, 106, 253) 0%, rgb(9, 175, 251) 100%); color: #fff; }
    .cc-winner-draw-bg { display: block; width: 100%; height: 100%; -o-object-fit: cover; object-fit: cover; position: absolute; top: 0; left: 0; z-index: 0; }
    
    .cc-winner-draw-stage { position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 10; overflow: hidden; padding: 6rem 10.8rem; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; }
    .cc-winner-draw-logo { display: block; width: 40.8rem; height: 19.8rem; -o-object-fit: contain; object-fit: contain; position: relative; margin: 0 0 3rem 0; z-index: 1; }
    .cc-winner-draw-prize-title { font-size: 4.4rem; font-style: normal; font-weight: 300; line-height: 125%; color: #fff; margin: 0 0 3rem 0; max-width: 55.2rem; }
    .cc-winner-draw-prize-image { display: block; width: 26.4rem; height: 26.4rem; -o-object-fit: contain; object-fit: contain; margin: 0 0 3rem 12rem; }
    .cc-winner-draw-name { font-size: 5.2rem; font-style: normal; font-weight: 300; line-height: 100%; color: #fff; margin: 0 0 3rem 0; display: -webkit-box; display: -ms-flexbox; display: flex;  max-width: 55.2rem; flex-wrap: wrap; }
    .cc-winner-draw-name svg { display: block; width: 1em; height: 1em; -o-object-fit: contain; object-fit: contain; margin: 0; padding: 0; line-height: 1; }
    .cc-winner-draw-users { display: block; margin: auto 0 0 0; max-width: 55.2rem; padding: 0.8rem 0 0 0; font-size: 4rem; font-style: normal; font-weight: 700; line-height: 125%; color: #fff; letter-spacing: 0.05em; }
    .cc-winner-draw-users * { margin: 0; padding: 0; }
    .cc-winner-draw-card { display: none; }
    .cc-winner-draw-card.active { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; -webkit-box-flex: 1; -ms-flex: 1; flex: 1; }
    </style>

    <title>Das große Days of Play 2025 Gewinnspiel</title>

    <meta name="description" content="Spiel täglich Mini-Games, sammle Tickets - und hol dir jeden Tag große Preise! Mach jetzt mit und lass dir die Chance nicht engehen.">

    <link rel="preload" href="{{ url('public/gfx/winner-draw-bg.jpg') }}" as="image">
    <link rel="preload" href="{{ $prize_image }}" as="image">
</head>

<body>

    <h1 class="visually-hidden">Das große Days of Play 2025 Gewinnspiel</h1>

    <!-- Start [ winner-draw ] -->
    <div class="cc-winner-draw">
        <img class="cc-winner-draw-bg" src="{{ url('public') }}/gfx/winner-draw-bg.jpg" alt="winner-draw bg">

        <div class="cc-winner-draw-stage">
            <img class="cc-winner-draw-logo" src="{{ url('public') }}/gfx/winner-draw-logo.svg" alt="cc-winner-draw logo">
            
            <!-- Start [ main-prize ] -->
            <div id="main" class="cc-winner-draw-card cc-winner-draw-main-prize">
                <h2 class="h2 cc-winner-draw-prize-title">{!! $prize_name !!}</h2>
                <img class="cc-winner-draw-prize-image" src="{{ $prize_image }}" alt="cc-winner-draw main prize image">
                <div class="cc-winner-draw-name"></div>
            </div>
            <!-- End  [ main-prize ] -->

            <!-- Start [ follow-prize ] -->
            <div id="follow" class="cc-winner-draw-card cc-winner-draw-follow-prize">
                <h2 class="h2 cc-winner-draw-prize-title">{!!  fT('d_pages.z_winner_screen.b_10_euro_prize_name', '10 Euro PSN-Guthaben')  !!}</h2>
                <img class="cc-winner-draw-prize-image" src="{{ $prize_image }}" alt="cc-winner-draw follow-up prize image">
                <div class="cc-winner-draw-name">USERNAME</div>
                <div class="cc-winner-draw-users"></div>
            </div>
            <!-- End  [ follow-prize ] --> 
        </div>
    </div>
    <!-- End [ winner-draw ] -->
    
    <script src="{{ url('public/js/jquery.min.js') }}"></script>
    <script src="{{ url('public/js/gsap.min.js') }}"></script>
    <script src="{{ url('public/js/MorphSVGPlugin.min.js') }}"></script>
    
    <script>
    gsap.registerPlugin(MorphSVGPlugin);

    function cc_winner_draw(type, users, title, image) {

        var screenType = type == 'main' ? 'main' : 'follow',
        user = users[0],
        screenEle = jQuery('#'+screenType),
        userEle = screenEle.find('.cc-winner-draw-name'),
        titleEle = screenEle.find('.cc-winner-draw-prize-title'),
        imageEle = screenEle.find('.cc-winner-draw-prize-image');

        jQuery('.cc-winner-draw-card').removeClass('active');
        gsap.set('.cc-winner-draw-card > *', { opacity: 0, y: 100 });

        screenEle.addClass('active');

        // set ps shape as per username langth
        userEle.html(cc_winner_draw_set_shape(screenType, user));

        titleEle.html(title);
        imageEle.attr('src', image);

        imageEle.on('load', function() {

            gsap.to('.cc-winner-draw-card > *', { duration: 0.5, opacity: 1, y: 0, stagger: 0.1, ease: "power1.out" });

            setTimeout(function() {
                // play animation shape to username
                cc_winner_draw_play_shape_animation(screenType, users, screenEle, userEle);
            }, 2000);
        
        }).attr('src', image);
    }


    function cc_winner_draw_set_shape(screenType, user) {

        var svgEle = '';

        for (var i = 0; i < user.length; i++) {
            
            if(i % 4 == 0) {
                svgEle += '<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="55px" height="55px" viewBox="0 0 55 55" style="enable-background:new 0 0 55 55;" xml:space="preserve"><path id="'+screenType+'-shape-'+i+'" d="M27.1,5L1,51h53.3L27.1,5z M27.2,22.9L38.5,42H16.4L27.2,22.9z" fill="#00DDCE"></path></svg>';
            }

            if(i % 4 == 1) {
                svgEle += '<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="55px" height="55px" viewBox="0 0 55 55" style="enable-background:new 0 0 55 55;" xml:space="preserve"><path id="'+screenType+'-shape-'+i+'" d="M27.8,5C15.2,5,5,15.2,5,27.7c0,12.5,10.2,22.7,22.8,22.7s22.8-10.2,22.8-22.7C50.5,15.2,40.3,5,27.8,5z M27.8,40.9c-7.2,0-13.1-5.9-13.1-13.1c0-7.2,5.9-13.1,13.1-13.1s13.1,5.9,13.1,13.1C40.9,35,35,40.9,27.8,40.9z" fill="#FD3E81"></path></svg>';
            }

            if(i % 4 == 2) {
                svgEle += '<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="55px" height="55px" viewBox="0 0 55 55" style="enable-background:new 0 0 55 55;" xml:space="preserve"><path id="'+screenType+'-shape-'+i+'" d="M34.5,27.9l14.9,14.9l-6.9,6.8L27.6,34.7L13,49.4l-6.9-6.8l14.7-14.6L6,13.1l6.9-6.9L27.6,21L42.7,6l6.9,6.8 L34.5,27.9z" fill="#33ADFE"></path></svg>';
            }

            if(i % 4 == 3) {
                svgEle += '<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="55px" height="55px" viewBox="0 0 55 55" style="enable-background:new 0 0 55 55;" xml:space="preserve"><path id="'+screenType+'-shape-'+i+'" d="M7,7v41.8h41.8V7H7z M39.5,39.5H16.3V16.3h23.3V39.5z" fill="#FF3DFE"></path></svg>';
            }
        }

        return svgEle;
    }


    function cc_winner_draw_play_shape_animation(screenType, users, screenEle, userEle) {

        var stringObj = {
            'a': 'M35.2,28.1c0-5.6-2.6-8.1-7.9-8.1c-2.4,0-5.2,0.5-7.6,1.6L19,19c2.3-0.9,5.4-1.6,8.5-1.6c7,0,10.9,3.4,10.9,10.4v12.3 c0,2,0.2,4.9,0.5,7h-2.9c-0.3-1.6-0.5-3.8-0.5-5.6h-0.1c-1.4,3.4-4.7,6.2-10.1,6.2c-4.8,0-9.2-2.7-9.2-8.6c0-8,8.2-9.7,19.1-9.7 V28.1z M33.9,31.8c-7.9,0-14.7,1.3-14.7,7.2c0,3.7,2.2,6.2,6.4,6.2c6.8,0,9.7-5.2,9.7-10.6v-2.8H33.9z',
            'b': 'M18,47.1h-3V6.7h3.1v17.1h0.1c1.6-3.5,5.2-6.4,9.8-6.4c7.5,0,11.9,6.7,11.9,15.1c0,8.5-4.5,15.2-12.1,15.2 c-4.5,0-8-2.6-9.7-6.1H18V47.1z M27.7,20c-6.1,0-9.7,5.2-9.7,12.5c0,7.3,3.7,12.6,9.6,12.6c6.1,0,9.2-5.7,9.2-12.6 C36.8,25.6,33.7,20,27.7,20z',
            'c': 'M37.2,21.1c-1.7-0.8-3.6-1.1-5.7-1.1c-7.3,0-11.2,5.6-11.2,12.6c0,6.9,3.9,12.6,11.2,12.6c2.1,0,3.9-0.3,5.9-1.1l0.5,2.5 c-2.1,0.8-4.2,1.2-6.5,1.2c-9.3,0-14.2-6.7-14.2-15.2c0-8.6,5.3-15.2,14.3-15.2c2.5,0,4.6,0.4,6.4,1.1L37.2,21.1z',
            'd': 'M36.9,6.7H40v40.4h-3.1v-5.7h-0.1c-1.8,3.7-5.4,6.4-10,6.4C19.3,47.8,15,41,15,32.6c0-8.5,4.5-15.1,12-15.1 c4.5,0,7.8,2.4,9.7,6.1h0.1V6.7z M27.3,45.2c6,0,9.7-5.3,9.7-12.6c0-7.3-3.8-12.6-9.7-12.6c-6.1,0-9.2,5.7-9.2,12.5 C18.1,39.5,21.3,45.2,27.3,45.2z',
            'e': 'M39.8,33.3H18.3c0.2,6.5,3.9,11.9,11.4,11.9c2.7,0,5.6-0.5,7.7-1.4l0.5,2.5c-2.3,0.9-5.4,1.4-8.4,1.4 c-9.5,0-14.4-6.6-14.4-15.4c0-8.1,4.5-15,12.8-15c8.2,0,11.8,5.8,11.8,14.1V33.3z M36.9,30.5c0-6.2-3-10.4-8.9-10.4 c-6.5,0-9.3,5.2-9.7,10.7h18.6V30.5z',
            'f': 'M36.8,9.8c-1.4-0.4-3-0.5-4.2-0.5c-3.9,0-5.2,2-5.2,7v1.8h7.4v2.6h-7.4v26.5h-3.1V20.7h-6.3v-2.6h6.3v-2.4 c0-5.6,2.1-9,8.3-9c1.8,0,3.2,0.2,4.5,0.6L36.8,9.8z',
            'g': 'M40,42.9C40,51,34.7,55,27,55c-2.7,0-6.2-0.5-8.6-1.5l0.7-2.6c2.3,1,5.2,1.5,8,1.5c6.7,0,10-3.5,10-9.7v-4.9h-0.1 c-1.8,3.4-5.1,6.1-9.8,6.1c-7.6,0-12-6.4-12-14.7c0-8.4,4.6-14.7,12.2-14.7c4.6,0,8,2.5,9.7,6H37v-5.4h3V42.9z M27.4,41.4 c6,0,9.6-4.9,9.6-12.1c0-7.2-3.8-12.1-9.6-12.1c-6.2,0-9.3,5.3-9.3,12.1C18.1,36.1,21.3,41.4,27.4,41.4z',
            'h': 'M19.2,6.7V23h0.1c1.7-3.6,5.3-5.6,9.4-5.6c6.7,0,10.3,4.3,10.3,10.7v19h-3.1V29.2c0-5.5-2-9.2-7.8-9.2 c-4.6,0-8.9,3-8.9,10.2v16.9H16V6.7H19.2z',
            'i': 'M29.3,7.2v3.8h-3.6V7.2H29.3z M29.1,18.1v29h-3.1v-29H29.1z',
            'j': 'M32.4,47c0,5.1-2.2,8-7.4,8c-0.7,0-1.6-0.1-2.6-0.2v-2.5c0.9,0.1,1.5,0.2,2.3,0.2c3.7,0,4.6-2.1,4.6-5.9V15.2h3.1V47z M32.6,4.3v3.8H29V4.3H32.6z',
            'k': 'M37.4,18.1l-13.9,13l14.5,16h-4.2L20.2,31.8h-0.1v15.3H17V6.7h3.1v24h0.1l13-12.6H37.4z',
            'l': 'M29.1,6.7v40.4h-3.1V6.7H29.1z',
            'm': 'M10.2,18.1v5.1c1.6-3.6,5.3-5.7,9.2-5.7c4.6,0,7.5,1.8,8.7,5.4h0.3c1.8-3.8,5.2-5.4,9.4-5.4c6.8,0,9.9,4.5,9.9,10.8v18.9 h-3.1V29.4c0-5.4-1.7-9.3-7.5-9.3c-4.1,0-8.1,2.7-8.1,8.7v18.4H26V28.8c0-5.4-1.5-8.8-7.2-8.8c-4.3,0-8.4,3-8.4,9.8v17.3H7.2v-29 H10.2z',
            'n': 'M19.1,18.1V23h0.1c1.7-3.6,5.4-5.6,9.5-5.6c6.7,0,10.3,4.3,10.3,10.7v19h-3.2V29.2c0-5.5-1.9-9.2-7.8-9.2 c-4.6,0-8.9,3-8.9,10.2v16.9H16v-29H19.1z',
            'o': 'M27.5,47.8c-8.2,0-13.1-6-13.1-15.2c0-9.2,4.8-15.1,13.1-15.1c8.2,0,13,5.9,13,15.1C40.6,41.8,35.7,47.8,27.5,47.8z M27.5,20c-6.8,0-10,5.4-10,12.5c0,7.1,3.2,12.6,10,12.6c6.8,0,9.9-5.5,9.9-12.6C37.4,25.4,34.3,20,27.5,20z',
            'p': 'M18.2,55H15V15.2h3V21h0.1c1.7-3.6,5.3-6.4,10-6.4c7.5,0,11.9,6.7,11.9,15.2c0,8.5-4.5,15.2-12.1,15.2 c-4.5,0-7.8-2.6-9.6-6.1h-0.1V55z M27.7,17.2c-6.1,0-9.7,5.3-9.7,12.6c0,7.3,3.7,12.6,9.6,12.6c6.1,0,9.2-5.7,9.2-12.6 C36.8,22.8,33.7,17.2,27.7,17.2z',
            'q': 'M36.9,15.2H40V55h-3.1V38.6h-0.1c-1.9,3.7-5.4,6.4-9.9,6.4c-7.6,0-11.9-6.8-11.9-15.2c0-8.5,4.6-15.1,12.1-15.1 c4.5,0,7.8,2.3,9.7,6.1h0.1V15.2z M27.3,42.4c6,0,9.7-5.3,9.7-12.6c0-7.3-3.8-12.6-9.7-12.6c-6.1,0-9.2,5.7-9.2,12.5 C18.1,36.7,21.3,42.4,27.3,42.4z',
            'r': 'M33.9,20.2c-0.7-0.1-1.4-0.2-2.3-0.2c-4.5,0-7.6,3.4-7.6,10.1v17h-3.1v-29h3V23H24c1.2-3.5,4.2-5.6,8-5.6 c0.7,0,1.4,0,2.1,0.1L33.9,20.2z',
            's': 'M35.1,21.5c-2.1-1-4.6-1.5-7-1.5c-3.3,0-6.5,1.2-6.5,4.8c0,3.1,2,4.4,6.5,6.1c5.2,1.9,9,3.9,9,8.8c0,5.7-4.7,8-10,8 c-3.1,0-6.7-0.8-9.2-2.1l0.9-2.5c2.6,1.3,5.8,2,8.6,2c4,0,6.6-1.5,6.6-5.3c0-3.3-2.9-4.8-7.4-6.5c-5.1-1.9-8.1-3.9-8.1-8.2 c0-5.3,4.8-7.7,9.6-7.7c2.8,0,5.7,0.5,7.8,1.6L35.1,21.5z',
            't': 'M19.6,18.1h5.4v-7.1h3.1v7.1h7v2.6h-7V40c0,4,0.8,4.9,4.1,4.9c1.2,0,2.1-0.1,3.1-0.2v2.4c-0.9,0.2-2.4,0.4-3.8,0.4 c-5,0-6.5-2.2-6.5-7.3V20.7h-5.4V18.1z',
            'u': 'M35.9,47.1v-4.9h-0.1c-1.7,3.6-5.4,5.6-9.5,5.6c-6.7,0-10.3-4.3-10.3-10.7v-19h3.1V36c0,5.5,2,9.2,7.8,9.2 c4.6,0,8.9-3,8.9-10.2V18.1H39v29H35.9z',
            'v': 'M14.5,18.1h3.4l9.6,26.3h0.1l9.5-26.3h3.4l-11.1,29h-3.7L14.5,18.1z',
            'w': 'M27.6,20.8h-0.1l-7.3,26.3H16l-8.6-29h3.4l7.3,26.3h0.1l7.1-26.3h4.4l7.2,26.3H37l7.2-26.3h3.4l-8.6,29h-4.2L27.6,20.8z',
            'x': 'M15.2,47.1l10.4-15.1l-9.5-14h3.6L27.6,30l8-11.9h3.5L29.5,32l10.3,15.1h-3.6l-8.7-13l-8.7,13H15.2z',
            'y': 'M27.7,40.2h0.1l9.3-25h3.4L24.8,55h-3.3L26,43.8L14.5,15.2h3.4L27.7,40.2z',
            'z': 'M34,20.7H17.9v-2.6h19.6v2.5l-17,23.9h17.7v2.6H16.9v-2.5L34,20.7z',
            'A': 'M13.6,47.1h-3.4L25.4,7.9h4.4l15,39.3h-3.5l-4.9-12.5h-18L13.6,47.1z M19.3,31.9h16.3l-8.1-21.6L19.3,31.9z',
            'B': 'M14.9,7.9h12.3c7,0,11.9,3.1,11.9,9.9c0,5.1-3.2,8-7.5,9v0.1c4.5,0.8,8.6,3.5,8.6,9.5c0,6.2-4.1,10.8-12.4,10.8H14.9V7.9z M18.1,25.5h8.1c5.9,0,9.6-2.4,9.6-7.6c0-5.6-4.1-7.3-9.2-7.3h-8.5V25.5z M18.1,44.4h9.1c6,0,9.7-2.4,9.7-8.1c0-6-4.6-8-10.5-8h-8.3 V44.4z',
            'C': 'M40.2,11.7c-2.3-1.2-5.1-1.8-8-1.8c-10.3,0-15.7,7.7-15.7,17.7c0,9.6,5.2,17.4,15.6,17.4c3.6,0,6.4-0.8,8.6-2l0.9,2.6 c-2.7,1.4-6,2.2-9.7,2.2c-11.9,0-18.6-8.5-18.6-20.2c0-11.7,6.5-20.3,18.9-20.4c3.3-0.1,6.7,0.7,9,1.9L40.2,11.7z',
            'D': 'M12.8,7.9h9.9c12-0.1,19.5,6.9,19.5,19.6c0,12.7-7.4,19.6-19.5,19.6h-9.9V7.9z M16,44.4h6.3c11.1,0,16.8-6.2,16.8-16.9 c0-10.7-5.7-16.9-16.8-16.9H16V44.4z',
            'E': 'M16.8,7.9h20.9v2.7H20v14.9h16.4v2.8H20v16.1h18.2v2.7H16.8V7.9z',
            'F': 'M17.4,7.9h20.1v2.7H20.6v14.9h15.9v2.8H20.6v18.9h-3.2V7.9z',
            'G': 'M41,12.2c-2.7-1.4-5.7-2.3-9.4-2.3c-10.8,0-16.4,7.9-16.4,17.5C15.2,37.1,21,45,31.5,45c3.2,0,6-0.7,8.2-1.9V29.3h-9.1v-2.8 H43v18.4c-3.8,2.1-7.9,2.9-11.7,2.9C19.2,47.8,12,39.1,12,27.4C12,15.8,19.3,7,31.7,7.2C35.7,7.2,39.1,8,42,9.4L41,12.2z',
            'H': 'M13.9,7.9h3.2v17.7h20.8V7.9h3.2v39.3h-3.2V28.3H17.1v18.9h-3.2V7.9z',
            'I': 'M25.9,7.9h3.2v39.3h-3.2V7.9z',
            'J': 'M33.3,39.5c0,4.8-1.8,8.3-7.8,8.3c-1,0-2.8-0.1-3.8-0.3v-2.7c1,0.2,2.4,0.3,3.3,0.3c3.6-0.1,5.1-1.7,5.1-5.7V7.9h3.2V39.5z',
            'K': 'M39.3,7.9L21.5,26.7l18.6,20.5h-4.2L18.1,27.4H18v19.7h-3.2V7.9H18v18.4h0.1l17-18.4H39.3z',
            'L': 'M17.6,7.9h3.2v36.5h16.7v2.7H17.6V7.9z',
            'M': 'M27.4,44.2h0.1l13-36.3h5.5v39.3h-3.1V10.3h-0.1L29.4,47.1h-4.1L12.2,10.3h-0.1v36.8h-3V7.9h5.5L27.4,44.2z',
            'N': 'M38.1,43.8h0.1v-36h3v39.3h-4.9L16.9,11.1h-0.1v36.1h-3V7.9h4.9L38.1,43.8z',
            'O': 'M27.5,47.8c-10.8,0-17.1-8.4-17.1-20.2c0-11.9,6.2-20.3,17.1-20.3c10.9,0,17,8.4,17,20.3C44.6,39.4,38.3,47.8,27.5,47.8z M27.5,10c-9.2,0-13.9,7.9-13.9,17.6c0,9.6,4.6,17.5,13.9,17.5c9.2,0,13.9-8,13.9-17.5C41.4,17.8,36.7,10,27.5,10z',
            'P': 'M15.2,7.9H27c6.6,0,12.9,2.3,12.9,10.7c0,8.9-6.2,11.3-12.8,11.3h-8.7v17.3h-3.2V7.9z M18.3,27.1h8.4c4.9,0.1,10-1.4,10-8.4 c0-6.9-4.8-8.1-10.3-8.1h-8V27.1z',
            'Q': 'M35.9,45.7l7,7.9h-4.2L33,46.9c-1.7,0.5-3.5,0.9-5.5,0.9c-10.8,0-17.1-8.4-17.1-20.2c0-11.9,6.2-20.3,17.1-20.3 c10.9,0,17,8.4,17,20.3C44.6,35.8,41.5,42.5,35.9,45.7z M27.5,10c-9.2,0-13.9,7.9-13.9,17.6c0,9.6,4.6,17.5,13.9,17.5 c9.2,0,13.9-7.9,13.9-17.5C41.4,17.8,36.7,10,27.5,10z',
            'R': 'M14.1,7.9h12.7c7.1,0,11.9,3,11.9,10.2c0,5.1-3.1,8.2-7.5,9.2v0.1c3.1,0.7,4.6,2.8,5.9,7.5c1.3,4.3,2.4,8,3.7,12.3h-3.4 c-1-3.3-2.5-9.2-3.4-12c-1.5-4.9-3.1-6.4-8.8-6.4h-8v18.4h-3.2V7.9z M17.3,26H26c5.9,0,9.5-2.6,9.5-7.8c0-6-4.1-7.6-9.3-7.6h-9V26z',
            'S': 'M36.9,11.7c-2.6-1.2-5.7-1.8-8.6-1.8c-4.5,0-9,1.9-9,7c0,4.3,2.9,6.3,8.6,8.6c6.9,2.8,11.4,5.5,11.4,11.8 c0,7.3-5.8,10.5-12.5,10.5c-3.7,0-8-0.9-11.2-2.5l0.9-2.9c3.2,1.7,7,2.6,10.4,2.6c5.4,0,9.3-2.5,9.3-7.5c0-4.8-4.1-6.9-10-9.3 c-6.4-2.6-10.1-5.4-10.1-11.1c0-6.9,6.2-9.9,12.2-9.9c3.3,0,6.6,0.6,9.4,1.8L36.9,11.7z',
            'T': 'M26.1,10.6H13.7V7.9h27.6v2.7h-12v36.5h-3.2V10.6z',
            'U': 'M41.6,35.1c0,7.4-4.6,12.7-14.2,12.7c-9.6,0-14-5.3-14-12.7V7.9h3.2v27c0,6.4,3.2,10.2,10.8,10.2c7.6,0,10.9-4.1,10.9-10.2 v-27h3.2V35.1z',
            'V': 'M11.2,7.9h3.4l12.9,36.3h0.1L40.4,7.9h3.4L29.5,47.1h-4.2L11.2,7.9z',
            'W': 'M27.5,10.4h-0.1l-8.6,36.7h-4.7L4.2,7.9h3.4l8.8,36.3h0.1L25,7.9h4.9l8.6,36.3h0.1l8.8-36.3h3.4l-10,39.3h-4.6L27.5,10.4z',
            'X': 'M25.7,26.7L13.3,7.9H17l10.6,16.5L38.4,7.9h3.6L29.5,26.7L43,47.1h-3.8L27.5,29.3L15.7,47.1H12L25.7,26.7z',
            'Y': 'M25.9,30.9l-14.5-23h3.8l12.3,20.1L40,7.9h3.6l-14.5,23v16.2h-3.2V30.9z',
            'Z': 'M36.5,10.6H16.1V7.9h24.2v2.7L17.9,44.4h22.8v2.7H14.3v-2.7L36.5,10.6z',
            '0': 'M41.2,27.6c0,12-4.3,20.2-13.7,20.2c-9.6,0-13.7-8.1-13.7-20.2c0-12,4.1-20.4,13.7-20.4C36.9,7.2,41.2,15.6,41.2,27.6z M17,27.6C17,38,19.9,45,27.5,45C34.9,45,38,38,38,27.6C38,17.1,34.9,10,27.5,10C19.9,10,17,17.1,17,27.6z',
            '1': 'M34.1,47.1h-3.2V10.9l-8.5,5.8l-1.5-2.3l9.6-6.5h3.5V47.1z',
            '2': 'M39.6,47.1H15.4v-2.9c17-15.9,20.5-20.4,20.5-26.4c0-4.9-3.8-7.9-8.8-7.9c-3.7,0-6.7,1.5-9.3,3.8l-1.5-2.3 c3-2.9,6.8-4.3,10.8-4.3c6.8,0,11.9,3.6,11.9,10.5c0,7.4-3.7,11.6-19.6,26.7h20.2V47.1z',
            '3': 'M20.7,25.5h3.7c6.2,0,11-2.5,11-8.1c0-4.9-3.6-7.5-8.7-7.5c-3.2,0-6.4,0.7-9.3,2.5l-1-2.6c3.1-1.8,6.9-2.6,10.6-2.6 c6.8,0,11.8,3.7,11.8,10c0,5.1-3.1,8.2-8.1,9.5v0.1c5.5,0.8,9.2,4.1,9.2,9.7c0,7.1-5.6,11.3-13.3,11.3c-4.2,0-8.3-1-11.2-2.6l1-2.6 c2.9,1.6,6.2,2.5,10.2,2.5c5.7,0,10-2.6,10-8.5c0-6.3-6-8.2-12.2-8.2h-3.7V25.5z',
            '4': 'M35.8,34.4h5.8v2.7h-5.8v10h-3.2v-10H13.4v-3.4L30.8,7.9h5V34.4z M32.6,10.6h-0.1L16.6,34.4h16.1V10.6z',
            '5': 'M25.1,26c-2.7,0-5.1,0.2-8,0.8l0.8-18.9h19.5v2.7H20.7l-0.6,13.1c2-0.3,4.2-0.4,5.5-0.4c7.5,0,14,3.8,14,11.8 c0,8.5-6.1,12.7-13.9,12.7c-3.7,0-7.1-0.7-10.2-2.1l1-2.7c2.8,1.3,5.8,2,9.2,2c6.2,0,10.8-3.1,10.8-9.8C36.4,28.4,30.7,26,25.1,26z',
            '6': 'M17.8,27.5c1.8-3.2,5.8-5.7,10.5-5.7c7.5,0,12.2,5.5,12.2,12.8c0,7.6-5.1,13.2-12.6,13.2c-10.2,0-13.3-9-13.3-17.5 c0-12.5,4.2-23.1,15.2-23.1c2.7,0,5.3,0.6,7.4,1.4l-0.8,2.6c-1.9-0.8-4.2-1.2-6.5-1.2c-8.9,0-11.7,9-12.1,17.5H17.8z M27.7,45 c5.9,0,9.6-4.3,9.6-10.4c0-5.5-3.2-10-9.2-10c-5.9,0-10.2,4.2-10.2,8.9C17.9,38.6,20.6,45,27.7,45z',
            '7': 'M15.8,7.9h23.4v2.7c-7.9,11.8-12.4,26.1-13.5,36.5h-3.2c1.1-10.8,6.2-25.8,13.3-36.5H15.8V7.9z',
            '8': 'M39.6,17.4c0,5.4-3.2,8.1-7,9.1v0.1c4.3,1.2,8.4,4.1,8.4,10.2c0,7.5-6.3,10.9-13.5,10.9c-7.2,0-13.4-3.4-13.4-10.9 c0-5.8,4.2-9.1,8.5-10.1v-0.1c-3.7-0.9-7.1-3.9-7.1-9.2c0-6.9,5.9-10.2,12-10.2C33.7,7.2,39.6,10.6,39.6,17.4z M17.2,36.6 c0,5.8,4.6,8.4,10.2,8.4c5.7,0,10.3-2.6,10.3-8.4c0-5.9-5.4-8.5-10.3-8.5C22.6,28.1,17.2,30.7,17.2,36.6z M36.4,17.4 c0-4.6-3.6-7.5-9-7.5c-5.2,0-8.7,2.9-8.7,7.5c0,5.7,4.2,8,8.8,8C32.1,25.4,36.4,23.2,36.4,17.4z',
            '9': 'M37.2,27.5c-1.8,3.3-5.8,5.7-10.5,5.7c-7.5,0-12.2-5.5-12.2-12.8c0-7.6,5.1-13.2,12.6-13.2c10.2,0,13.4,9,13.4,17.5 c0,12.5-4.2,23.1-15.2,23.1c-2.5,0-5.8-0.4-8-1.4l0.7-2.6c2.1,0.8,4.9,1.3,7.2,1.3c8.9,0,11.8-9,12.1-17.5H37.2z M27.3,10 c-5.9,0-9.6,4.3-9.6,10.4c0,5.5,3.2,10,9.2,10c6,0,10.2-4.2,10.2-8.9C37.1,16.4,34.4,10,27.3,10z',
            ' ': 'M0,0h55v55H0V0z',
            '.': 'M25.4,42.7h4.1v4.5h-4.1V42.7z',
            ',': 'M27,42.5h3.5l-4.1,12.6h-2L27,42.5z',
            '!': 'M25.5,42.8h4v4.3h-4V42.8z M25.6,7.9h3.8l-0.7,29h-2.5L25.6,7.9z',
            '?': 'M26.9,38.2H24v-2c0-4.7,1.2-7.3,4.2-10l2.1-1.9c2.3-2,4-4.5,4-7.6c0-4.9-3.7-6.8-8-6.8c-2.6,0-5.3,0.8-7.8,2.2l-1-2.5 c2.6-1.5,5.4-2.4,9-2.4c5.8,0,10.9,2.5,10.9,9.4c0,3.8-1.8,6.7-4.5,9.1L31,27.4c-3.1,2.8-4.2,4.5-4.2,9V38.2z M23.3,47.1v-4.3h4.2 v4.3H23.3z',
            '#': 'M20,35h-5.9v-2.4h6.2l1.4-10.3h-6.3V20h6.6l1.6-12.1h2.4L24.5,20h8.7l1.7-12.1h2.4L35.6,20h5.2v2.4h-5.6l-1.4,10.3h5.5V35 h-5.8l-1.7,12.2h-2.5L31.1,35h-8.6l-1.7,12.2h-2.4L20,35z M22.8,32.6h8.6l1.5-10.3h-8.7L22.8,32.6z',
            '@': 'M42.8,52.1c-4,1.9-8.8,2.9-14,2.9C13.5,55,4.7,44.7,4.7,30.1c0-14.5,10.3-25,23.8-25c11.8,0,21.9,6.7,21.9,21.2 c0,10.3-4.8,16.7-11.2,16.7c-4.3,0-5.6-3.2-5.6-5.7h-0.1c-0.8,1.9-3.6,5.7-8.4,5.7c-5.9,0-8.6-4.8-8.6-11.1 c0-7.5,4.6-15.1,11.9-15.1c4.1,0,6.3,2.9,6.9,5.8h0.1l1-4.8h2.6l-2.5,12.4c-0.3,1.8-0.7,4.2-0.7,5.7c0,2,0.4,4.6,3.7,4.6 c4.7,0,7.9-6.2,7.9-13.9c0-12.5-8.2-18.9-19.1-18.9c-12.1,0-20.7,9.7-20.7,22.5c0,13.5,8.2,22.2,21.4,22.2c5,0,9.3-1,13-2.7 L42.8,52.1z M19.5,32c0,3.6,1.3,8.4,6,8.4c6.3,0,8.6-8.3,8.6-13.1c0-3.9-1.2-8-5.8-8C22.8,19.3,19.5,26.2,19.5,32z',
            '(': 'M32.6,55h-3c-4.2-6.9-7.1-16.6-7.1-27.3c0-10.7,2.9-20.3,7.1-27.2h3c-4.8,7.7-7.3,17.7-7.3,27.2 C25.2,37.3,27.7,47.3,32.6,55z',
            ')': 'M22.4,0.4h2.9c4.3,6.9,7.2,16.6,7.2,27.4c0,10.7-2.9,20.2-7.2,27.2h-2.9c4.8-7.7,7.3-17.7,7.3-27.2 C29.8,18.1,27.3,8.1,22.4,0.4z',
            '+': 'M28.8,17.3v11.3h11V31h-11v11.7h-2.5V31h-11v-2.4h11V17.3H28.8z',
            '-': 'M21.1,29.3h12.8v2.6H21.1V29.3z',
            '=': 'M15.2,26.1v-2.4h24.5v2.4H15.2z M15.2,35.9v-2.4h24.5v2.4H15.2z',
            '_': 'M40.3,52.6v1.9H14.7v-1.9H40.3z'
        }

        var tl = gsap.timeline({ 
            onComplete: function() {

                if(screenType == 'follow') {

                    jQuery('#'+screenType+' .cc-winner-draw-users').append('<p style="opacity: 0; transform: translateY(100px);">'+users[0]+'</p>');

                    gsap.to(userEle, {duration: 0.3, opacity: 0, y: -60, ease: "power1.out", onComplete: function() {

                        users.shift();

                        if(users.length > 0)  {
                            // loop: set ps shape as per username langth
                            userEle.html(cc_winner_draw_set_shape(screenType, users[0]));
                        }
                        else  {
                            jQuery(".cc-winner-draw-name").addClass('d-none');
                        }
                    }});
                    
                    gsap.to('#'+screenType+' .cc-winner-draw-users p:last-child', {duration: 0.4, opacity: 1, y: 0, ease: "power1.out", onComplete: function() {

                        gsap.fromTo(userEle, { opacity: 0, y: 60 }, {duration: 0.3, opacity: 1, y: 0, ease: "power1.out", onComplete: function() {

                            if(users.length > 0)  {

                                setTimeout(function() {
                                    // loop: play animation shape to username
                                    cc_winner_draw_play_shape_animation(screenType, users, screenEle, userEle);
                                }, 2000);

                            }

                        }});

                    }});
                }
            }
        }),
        userChars = users[0].split('');

        tl.to("#"+screenType+"-shape-0", {duration: 1, ease: "power1.out"});

        for (var i = 0; i < users[0].length; i++) {
            tl.to("#"+screenType+"-shape-"+i, {duration: 0.5, fill: '#ffffff', morphSVG: stringObj[userChars[i]], ease: "power1.out"});
        }
    }

    window.onload = function() {
        
        setTimeout(function() {
            cc_winner_draw('{{ $winner_type }}', {!! json_encode($winners) !!}, '{{ $prize_name }}', '{{ $prize_image }}');
            
            //cc_winner_draw('follow', ['USERNAME','NeoWolfX', 'ShadowStrike77', 'TrophySeeker', 'AstroRunner99'], '10 Euro PSN', '{{ url('public') }}/gfx/winner-draw-follow-up-prize.png');
        }, 3000);
    };
    </script>

</body>
</html>