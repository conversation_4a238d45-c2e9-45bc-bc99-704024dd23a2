@php
$id = $id ?? $name;
$id = str_replace("[", "-", $id);
$id = str_replace("]", "", $id);

$error_name = str_replace("]", "", str_replace("[", ".", $name));
@endphp

<div class="mb-4 cc-form-field-include {{ $additionalClass ?? "" }}">
    
    @if(isset($label) && $label)

        @if(isset($text_form) && $text_form)
                
            <div class="d-flex justify-content-between align-items-center mb-1">
                <div class="me-4">
                    <label for="{{ $id }}" class="form-label mb-0">
                        <span class="required">{!! $label !!}</span>
                    </label>
                </div>
                <a href="javascript:;" class="avatar-text avatar-md border-0 bg-soft-danger text-danger ms-auto delete-app-text" data-bs-toggle="tooltip" data-bs-original-title="Delete" data-key="{{ $name }}"><i class="feather-trash-2"></i></a>
            </div>

        @else

            <label for="{{ $id }}" class="form-label">
                {!! $label !!} 
                
                @if(isset($required) && $required)
                    <span class="text-danger">*</span>
                @endif
            </label>

        @endif
        
    @endif
    
    <div class="form-check form-switch form-switch-lg mb-4">
        <input type="checkbox" name="{{ $name }}" class="form-check-input c-pointer" value="{{ $value ?? "1" }}"
           @if(isset($checked) && $checked) checked="checked" @endif >
    </div>

    @if(isset($input_comment))
        <span class="ml-2">{{ $input_comment }}</span>
    @endif
</div>