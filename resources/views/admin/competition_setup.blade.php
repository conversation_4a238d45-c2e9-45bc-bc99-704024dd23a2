@extends('admin.layouts.layout')
@section('content')

    @php
    $start_timestamp = strtotime($start_date);
    $end_timestamp = strtotime($end_date);
    @endphp

    @section('custom_css')
        <link rel="stylesheet" type="text/css" href="{{ asset('public/assets') }}/vendors/css/datepicker.min.css">
    @endsection

    <div class="nxl-content">
        <!-- [ page-header ] start -->
        <div class="page-header">
            <div class="page-header-left d-flex align-items-center">
                <div class="page-header-title">
                    <h5 class="m-b-10">Days of Play Competition Setup</h5>
                </div>
            </div>
            <div class="page-header-right ms-auto">
                <div class="page-header-right-items">
                    <div class="d-flex d-md-none">
                        <a href="javascript:void(0)" class="page-header-right-close-toggle">
                            <i class="feather-arrow-left me-2"></i>
                            <span>Back</span>
                        </a>
                    </div>
                    <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                        <a href="javascript:void(0);" class="btn btn-primary cc-form-submit-btn">
                            <i class="feather-save me-2"></i>
                            <span>Save</span>
                        </a>
                    </div>
                </div>
                <div class="d-md-none d-flex align-items-center">
                    <a href="javascript:void(0)" class="page-header-right-open-toggle">
                        <i class="feather-align-right fs-20"></i>
                    </a>
                </div>
            </div>
        </div>
        <!-- [ page-header ] end -->

        <!-- [ Main Content ] start -->
        <div class="main-content">
            
            <div class="row">
                <div class="col-xl-12">
                    
                    <div class="card">
                        <div class="card-body">
                            
                            <form id="kt_competition_form" class="form" action="{{ route("admin_competition_save") }}" method="POST">

                                @if (session('suc_message'))
                                    <div class="alert alert-success">{{session('suc_message')}}</div>
                                @endif

                                @if (session('err_message'))
                                    <div class="alert alert-danger">{{session('err_message')}}</div>
                                @endif

                                <div class="row">
                                    @include('admin.form.text_component', ['name' => 'start_date', 'value' => (old('start_date') ?: (@$start_date ? date('m.d.Y', strtotime($start_date)) : "")), 'label' => "Start Date", 'required' => true, "inputCls" => "datepicker_field", 'additionalClass'=>'col-md-4'])

                                    @include('admin.form.text_component', ['name' => 'end_date', 'value' => (old('end_date') ?: (@$end_date ? date('m.d.Y', strtotime($end_date)): "")), 'label' => "End Date", 'required' => true, "inputCls" => "datepicker_field", 'additionalClass'=>'col-md-4'])
                                </div>
                                
                                <div class="row">
                                    @include('admin.form.text_component',  ['name' => 'share_title', 'value' => (old('share_title') ?: (@$share_title ?: "")), 'label' => "Share Title [URL]", 'required' => true, 'additionalClass'=>'col-md-4'])

                                    @include('admin.form.textarea_component', ['name' => 'share_description', 'value' => (old('share_description') ?: (@$share_description ?: "")), 'label' => "Share Text [URL]", 'editor' => false, 'required' => true, 'additionalClass'=>'col-md-8'])
                                </div>

                                <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                <button type="submit" class="d-none cc-form-submit">Submit</button>
                            </form>
                            
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-body">
                            
                            <h5 class="mb-4">Days of Play Competition Setup</h5>

                            <ul class="nav nav-tabs nav-line-tabs mb-5 fs-6">
                                @php $i=0 @endphp
                                @for ($timestamp = $start_timestamp; $timestamp <= $end_timestamp; $timestamp += 86400)
                                    
                                    @php
                                    $current_date = date('d.m.', $timestamp);
                                    @endphp    
                                    
                                    <li class="nav-item">
                                        <a class="nav-link {{ ($i==0) ? 'active' : '' }}" data-bs-toggle="tab" href="#kt_tab_pane_{{ $i }}">{!! $current_date !!}</a>
                                    </li>

                                    @php $i++ @endphp
                                @endfor
                            </ul>

                            <div class="tab-content" id="myTabContent">
                                    
                                @php $i=0 @endphp
                                @for ($timestamp = $start_timestamp; $timestamp <= $end_timestamp; $timestamp += 86400)
                                    
                                    @php
                                    $current_date = date('Y-m-d', $timestamp);
                                    $date_slug = date('dmY', $timestamp);
                                    @endphp
                                    
                                    <div class="tab-pane fade {{ ($i==0) ? 'show active' : '' }}" id="kt_tab_pane_{{ $i }}" role="tabpanel">
                                        
                                        <form id="kt_competition_day_form_{{ $date_slug }}" class="form kt_competition_day_form" action="{{ route("admin_competition_day_save") }}" method="POST">

                                            <div class="cc-error-message text-danger"></div>

                                            <div class="row">
                                                <div class="col-md-7">
                                                    @include('admin.form.text_component',  ['name' => 'prize_name', 'value' => (old('prize_name') ?: (@$savedCompetitions[$current_date]['prize_name'] ?: "")), 'label' => "Prize Name", 'required' => true, 'id' => 'prize_name_'.$date_slug])

                                                    @include('admin.form.textarea_component', ['name' => 'prize_description', 'value' => (old('prize_description') ?: (@$savedCompetitions[$current_date]['prize_description'] ?: "")), 'label' => "Prize Description", 'editor' => false, 'required' => true, 'id' => 'prize_description_'.$date_slug])

                                                    <div class="row">
                                                        @include('admin.form.ajax_image_upload_component',  ['name' => 'prize_image', 'value' => (old('prize_image') ?: (@$savedCompetitions[$current_date]['prize_image'] ?: "")), 'label' => "Prize Image (1200 x 1200)", 'required' => true, 'id' => 'prize_image_'.$date_slug, 'additionalClass'=>'col-md-4'])

                                                        @include('admin.form.ajax_image_upload_component',  ['name' => 'main_prize_image', 'value' => (old('main_prize_image') ?: (@$savedCompetitions[$current_date]['main_prize_image'] ?: "")), 'label' => "Main Prize Image (1200 x 1200)", 'required' => true, 'id' => 'main_prize_image_'.$date_slug, 'additionalClass'=>'col-md-4'])
        
                                                        @include('admin.form.ajax_pdf_upload_component',  ['name' => 'terms_pdf', 'value' => (old('terms_pdf') ?: (@$savedCompetitions[$current_date]['terms_pdf'] ?: "")), 'label' => "Terms and Condition PDF", 'required' => true, 'id' => 'terms_pdf_'.$date_slug, 'additionalClass'=>'col-md-4'])
                                                    </div>
                                                </div>

                                                <div class="col-md-5 avtar-details">
                                                    <div class="row">
                                                        <div class="col-md-7">
                                                            @include('admin.form.text_component',  ['name' => 'avtar_name', 'value' => (old('avtar_name') ?: (@$savedCompetitions[$current_date]['avtar_name'] ?: "")), 'label' => "Avtar Name", 'required' => true, 'id' => 'avtar_name_'.$date_slug])

                                                            @include('admin.form.textarea_component',  ['name' => 'avtar_desc', 'value' => (old('avtar_desc') ?: (@$savedCompetitions[$current_date]['avtar_desc'] ?: "")), 'label' => "Avtar Description", 'required' => true, 'id' => 'avtar_desc_'.$date_slug])

                                                            @include('admin.form.radio_component', ['name' => 'code_type', 'checked' => (old('code_type') ?: (@$savedCompetitions[$current_date]['code_type'] ?: "fixed")), 'label' => 'Code Type', 'required' => true, 'values' => ['fixed' => 'Fixed', 'reward' => 'Reward Set'], 'id' => 'code_type_'.$date_slug])

                                                            @include('admin.form.text_component',  ['name' => 'avtar_code', 'value' => (old('avtar_code') ?: (@$savedCompetitions[$current_date]['avtar_code'] ?: "")), 'label' => "Avtar Code", 'required' => true, 'id' => 'avtar_code_'.$date_slug])

                                                            @include('admin.form.select_component',  ['name' => 'reward_id', 'value' => (old('reward_id') ?: (@$savedCompetitions[$current_date]['reward_id'] ? @$savedCompetitions[$current_date]['reward_id']."|".@$savedCompetitions[$current_date]['reward_code_type'] : "")), 'label' => "Avtar Code Set", 'required' => true, 'id' => 'reward_id_'.$date_slug,'options' => $rewards, 'optionNoneSelected' => "Select Code Set"])
                                                        </div>

                                                        <div class="col-md-5">
                                                            @include('admin.form.ajax_image_upload_component',  ['name' => 'avtar_image', 'value' => (old('avtar_image') ?: (@$savedCompetitions[$current_date]['avtar_image'] ?: "")), 'label' => "Avtar Image (800 x 800)", 'required' => true, 'id' => 'avtar_image_'.$date_slug])
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <hr>

                                            <div class="align-items-right">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="feather-save me-2"></i>
                                                    <span>Submit</span>
                                                </button>
                                            </div>

                                            <input type="hidden" name="current_date" value="{{ $current_date }}">
                                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                        </form>
                                    </div>

                                    @php $i++ @endphp
                                @endfor

                        </div>
                    </div>
                    
                </div>
            </div>

        </div>
        <!-- [ Main Content ] end -->
    </div>

    @section('custom_javascript')
        <script src="{{ asset('public/assets') }}/vendors/js/datepicker.min.js"></script>
    @endsection

@endsection