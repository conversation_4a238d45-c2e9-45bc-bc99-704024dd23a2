<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix("admin")->group(function () {

    // DashboardController
    Route::get('/', 'Admin\DashboardController@overview')
        ->name("admin_overview");
    Route::get('/export-stats', 'Admin\DashboardController@exportStats')
        ->name("admin_export_stats");

    // CompetitionController
    Route::get('/competition-setup', 'Admin\CompetitionController@index')
        ->name("admin_competition_setup");
    Route::post('/competition/save', 'Admin\CompetitionController@save')
        ->name("admin_competition_save");
    Route::post('/competition/day/save', 'Admin\CompetitionController@saveCompetitionDay')
        ->name("admin_competition_day_save");

    // Faqs
    Route::get('/faqs', 'Admin\FaqController@index')
        ->name("admin_faqs");
    Route::get('/faqs/list', 'Admin\FaqController@ajaxList')
        ->name("admin_ajax_faq_list");
    Route::get('/faqs/edit/{id?}', 'Admin\FaqController@edit')
        ->name("admin_faq_edit");
    Route::post('/faqs/save/{id?}', 'Admin\FaqController@save')
        ->name("admin_faq_save");
    Route::delete('/faqs/remove', 'Admin\FaqController@remove')
        ->name("admin_faq_remove");

    //Gift Codes
    Route::get('/gift-codes', 'Admin\GiftCodeController@index')
        ->name("admin_gift_codes");
    Route::get('/gift-codes/list', 'Admin\GiftCodeController@ajaxList')
        ->name("admin_ajax_gift_code_list");
    Route::get('/gift-codes/edit', 'Admin\GiftCodeController@edit')
        ->name("admin_gift_code_edit");
    Route::post('/gift-codes/save', 'Admin\GiftCodeController@save')
        ->name("admin_gift_code_save");
    Route::post('/import_giftcodes', 'Admin\GiftCodeController@importCodes')
        ->name("admin_import_giftcodes");

    //Trivia
    Route::get('/trivias', 'Admin\TriviaController@index')
        ->name("admin_trivias");
    Route::get('/trivias/list', 'Admin\TriviaController@ajaxList')
        ->name("admin_ajax_trivia_list");
    Route::get('/trivia/edit/{id?}', 'Admin\TriviaController@edit')
        ->name("admin_trivia_edit");
    Route::post('/trivia/save/{id?}', 'Admin\TriviaController@save')
        ->name("admin_trivia_save");
    Route::delete('/trivia/remove', 'Admin\TriviaController@remove')
        ->name("admin_trivia_remove");

    //Guess The Game
    Route::get('/guess_the_games', 'Admin\GuessTheGameController@index')
        ->name("admin_guess_the_games");
    Route::get('/guess_the_games/list', 'Admin\GuessTheGameController@ajaxList')
        ->name("admin_ajax_guess_the_game_list");
    Route::get('/guess_the_game/edit/{id?}', 'Admin\GuessTheGameController@edit')
        ->name("admin_guess_the_game_edit");
    Route::post('/guess_the_game/save/{id?}', 'Admin\GuessTheGameController@save')
        ->name("admin_guess_the_game_save");
    Route::delete('/guess_the_game/remove', 'Admin\GuessTheGameController@remove')
        ->name("admin_guess_the_game_remove");
    
    //Math Riddle
    Route::get('/math_riddles', 'Admin\MathRiddleController@index')
        ->name("admin_math_riddles");
    Route::get('/math_riddles/list', 'Admin\MathRiddleController@ajaxList')
        ->name("admin_ajax_math_riddle_list");
    Route::get('/math_riddle/edit/{id?}', 'Admin\MathRiddleController@edit')
        ->name("admin_math_riddle_edit");
    Route::post('/math_riddle/save/{id?}', 'Admin\MathRiddleController@save')
        ->name("admin_math_riddle_save");
    Route::delete('/math_riddle/remove', 'Admin\MathRiddleController@remove')
        ->name("admin_math_riddle_remove");

    //Memory
    Route::get('/memories', 'Admin\MemoryController@index')
        ->name("admin_memories");
    Route::get('/memories/list', 'Admin\MemoryController@ajaxList')
        ->name("admin_ajax_memory_list");
    Route::get('/memory/edit/{id?}', 'Admin\MemoryController@edit')
        ->name("admin_memory_edit");
    Route::post('/memory/save/{id?}', 'Admin\MemoryController@save')
        ->name("admin_memory_save");
    Route::delete('/memory/remove', 'Admin\MemoryController@remove')
        ->name("admin_memory_remove");

    //Find The Mistake
    Route::get('/find_the_mistakes', 'Admin\FindTheMistakeController@index')
        ->name("admin_find_the_mistakes");
    Route::get('/find_the_mistakes/list', 'Admin\FindTheMistakeController@ajaxList')
        ->name("admin_ajax_find_the_mistake_list");
    Route::get('/find_the_mistake/edit/{id?}', 'Admin\FindTheMistakeController@edit')
        ->name("admin_find_the_mistake_edit");
    Route::post('/find_the_mistake/save/{id?}', 'Admin\FindTheMistakeController@save')
        ->name("admin_find_the_mistake_save");
    Route::delete('/find_the_mistake/remove', 'Admin\FindTheMistakeController@remove')
        ->name("admin_find_the_mistake_remove");
    Route::post('/find_the_mistake/error-spots/', 'Admin\FindTheMistakeController@errorSpots')
        ->name("admin_find_the_mistake_image_form");
    
    //Game
    Route::get('/games', 'Admin\GameController@index')
        ->name("admin_games");
    Route::get('/games/list', 'Admin\GameController@ajaxList')
        ->name("admin_ajax_game_list");
    Route::get('/game/edit/{id?}', 'Admin\GameController@edit')
        ->name("admin_game_edit");
    Route::post('/game/save/{id?}', 'Admin\GameController@save')
        ->name("admin_game_save");
    Route::delete('/game/remove', 'Admin\GameController@remove')
        ->name("admin_game_remove");
    Route::get('/games/delete-all', 'Admin\GameController@deleteAll')
        ->name("admin_all_games_delete");

    //Day Of Play Offer
    Route::get('/day_of_play_offers', 'Admin\DayOfPlayOfferController@index')
        ->name("admin_day_of_play_offers");
    Route::get('/day_of_play_offers/list', 'Admin\DayOfPlayOfferController@ajaxList')
        ->name("admin_ajax_day_of_play_offer_list");
    Route::get('/day_of_play_offers/edit/{id?}', 'Admin\DayOfPlayOfferController@edit')
        ->name("admin_day_of_play_offer_edit");
    Route::post('/day_of_play_offers/save/{id?}', 'Admin\DayOfPlayOfferController@save')
        ->name("admin_day_of_play_offer_save");
    Route::delete('/day_of_play_offers/remove', 'Admin\DayOfPlayOfferController@remove')
        ->name("admin_day_of_play_offer_remove");

    //Participant
    Route::get('/participants', 'Admin\ParticipantController@index')
        ->name("admin_participants");
    Route::get('/participants/list/{competition_id}', 'Admin\ParticipantController@ajaxList')
        ->name("admin_ajax_participant_list");
    Route::get('/participants/export/{competition_id}', 'Admin\ParticipantController@exportParticipants')
        ->name("admin_export_participants");

    // Users
    Route::get('/users', 'Admin\UserController@index')
        ->name("admin_users");
    Route::get('/users/list', 'Admin\UserController@ajaxList')
        ->name("admin_ajax_user_list");
    Route::get('/users/edit/{id?}', 'Admin\UserController@edit')
        ->name("admin_user_edit");
    Route::post('/users/save/{id?}', 'Admin\UserController@save')
        ->name("admin_user_save");
    Route::post('/user/add-point/{id}', 'Admin\UserController@addPoints')
        ->name("admin_user_add_points");
    /*
    Route::delete('/users/remove', 'Admin\UserController@remove')
        ->name("admin_user_remove");*/
    Route::get('/users/export', 'Admin\UserController@exportUsers')
        ->name("admin_export_users");
    Route::get('/users/points', 'Admin\UserController@ajaxUserPointList')
        ->name("admin_ajax_user_points");

    //App Transactions
    Route::get('/transactions', 'Admin\TransactionController@index')
        ->name("admin_transactions");
    Route::get('/transactions/list', 'Admin\TransactionController@ajaxList')
        ->name("admin_ajax_transaction_list");
    Route::get('/transactions/edit/{id?}', 'Admin\TransactionController@edit')
        ->name("admin_transaction_edit");
    Route::post('/transactions/save/{id?}', 'Admin\TransactionController@save')
        ->name("admin_transaction_save");
    Route::delete('/transactions/remove', 'Admin\TransactionController@remove')
        ->name("admin_transaction_remove");
    Route::post('/transactions/mass_notifications', 'Admin\TransactionController@mass_notifications')
        ->name("admin_mass_notifications");
    
    // Digital Rewards
    Route::get('/rewards', 'Admin\RewardController@index')
        ->name("admin_rewards");
    Route::get('/rewards/list', 'Admin\RewardController@ajaxList')
        ->name("admin_ajax_reward_list");
    Route::get('/reward/export-codes/{reward_id}', 'Admin\RewardController@exportCodes')
        ->name("admin_export_codes");
    Route::get('/reward/get-new-codes/{reward_id}', 'Admin\RewardController@importNewCodes')
        ->name("admin_get_new_reward_codes");
    Route::get('/reward/revert-codes/{reward_id}', 'Admin\RewardController@revertCodes')
        ->name("admin_revert_reward_codes");

    // SettingController
    Route::get('/settings/{module?}', 'Admin\SettingController@settingForm')
        ->name("admin_settings");
    Route::post('/ajax_save_setting', 'Admin\SettingController@ajaxSaveSetting')
        ->name("admin_save_setting");
    Route::post('/ajax_get_translate', 'Admin\SettingController@ajaxGetTranslate')
        ->name("admin_get_translate");
    
    // UploadController
    Route::post('/gfx_upload', 'Admin\UploadController@ajaxImageUpload')
        ->name('admin_ajax_image_upload');
    Route::post('/pdf_upload', 'Admin\UploadController@ajaxPdfUpload')
        ->name('admin_ajax_pdf_upload');
    Route::post('/xls_upload', 'Admin\UploadController@ajaxXlsUpload')
        ->name('admin_ajax_xls_upload');
    Route::post('/video_upload', 'Admin\UploadController@ajaxVideoUpload')
        ->name('admin_ajax_video_upload');
    Route::post('/audio_upload', 'Admin\UploadController@ajaxAudioUpload')
        ->name('admin_ajax_audio_upload');
    Route::post('/media_upload', 'Admin\UploadController@ajaxMediaUpload')
        ->name('admin_ajax_media_upload');

    //Texts admin module
    Route::get('/app_texts/{module?}', 'Admin\TextController@appTextForm')
        ->name("admin_app_texts");
    Route::get('/app_emails/{module?}', 'Admin\TextController@appEmailForm')
        ->name("admin_app_emails");
    Route::post('/ajax_save_text', 'Admin\TextController@ajaxSaveText')
        ->name("admin_save_text");
    Route::post('/ajax_delete_text', 'Admin\TextController@ajaxDeleteText')
        ->name("admin_delete_text");
    Route::post('/import_texts', 'Admin\TextController@importTexts')
        ->name("admin_import_texts");
    Route::get('/export_texts/{module?}', 'Admin\TextController@exportTexts')
        ->name("admin_export_texts");
    Route::post('/send_preview_mail', 'Admin\TextController@sendPreviewEmail')
        ->name("admin_send_preview_mail");
    
    // AdminController
    Route::get('/list', 'Admin\AdminController@listAdmin')
        ->name('admin_list_admin');
    Route::get('/admin/ajax/list', 'Admin\AdminController@ajaxList')
        ->name('admin_ajax_admin_list');
    Route::get('/new-admin', 'Admin\AdminController@newAdmin')
        ->name('admin_new');
    Route::post('/save-admin', 'Admin\AdminController@saveAdmin')
        ->name('admin_save');
    Route::post('/save-admin/{id?}', 'Admin\AdminController@saveAdmin')
        ->name('admin_save_now');
    Route::get('/edit-admin/{id}', 'Admin\AdminController@editAdmin')
        ->name('admin_edit');
    Route::get('/regenerate/{id}', 'Admin\AdminController@regenerate')
        ->name('admin_regenerate');
    Route::delete('/delete-admin', 'Admin\AdminController@deleteAdmin')
        ->name('admin_delete');

    //Winners
    Route::get('/winners', 'Admin\WinnerController@index')
        ->name("admin_winners");
    Route::get('/winners/list', 'Admin\WinnerController@ajaxList')
        ->name("admin_ajax_winner_list");
    Route::post('/winner/claim_prize', 'Admin\WinnerController@claimPrize')
        ->name("admin_winner_claim_prize");
    Route::post('/winner/redraw', 'Admin\WinnerController@redrawWinner')
        ->name("admin_redraw_winner");
    Route::get('/winners/export', 'Admin\WinnerController@exportWinners')
        ->name("admin_export_winners");

    // Stream DashboardController
    Route::get("/stream/dashboard", 'Stream\DashboardController@dashboard')
        ->name("stream_dashboard");
    Route::post('/stream/event-broadcast', 'Stream\DashboardController@eventBroadcast')
        ->name('stream_event_broadcast');
    Route::post('/stream/view-chat-data', 'Stream\DashboardController@viewChatData')
        ->name('stream_view_chat_data');
    Route::post('/stream/keep-live-session', 'Stream\DashboardController@keepLiveSession')
        ->name('stream_keep_live_session');

    Route::get("/stream/show-comeptition-winners/{competition_id}", 'Stream\DashboardController@showCompetitionWinners')
        ->name("stream_show_comeptition_winners");
    /*Route::get("/stream/dashboard/draw-winner/{type}", 'Stream\DashboardController@drawWinner')
        ->name("stream_draw_winner");
    Route::get("/stream/winner-data-reset", 'Stream\DashboardController@winnerDataReset')
        ->name("stream_draw_winner_reset_data");
    Route::post('/stream/send-chat-message', 'Stream\DashboardController@sendChatMessage')
        ->name('stream_send_chat_message');*/
    
    //Extension Quizzes
    Route::get('/extension_quizzes', 'Admin\ExtensionQuizController@index')
         ->name("admin_extension_quizzes");
    Route::get('/extension_quizzes/list', 'Admin\ExtensionQuizController@ajaxList')
         ->name("admin_ajax_extension_quiz_list");
    Route::get('/extension_quizzes/edit/{id?}', 'Admin\ExtensionQuizController@edit')
         ->name("admin_extension_quiz_edit");
    Route::post('/extension_quizzes/save/{id?}', 'Admin\ExtensionQuizController@save')
         ->name("admin_extension_quiz_save");
    Route::delete('/extension_quizzes/remove', 'Admin\ExtensionQuizController@remove')
         ->name("admin_extension_quiz_remove");
    Route::get('/extension_quiz/broadcast/{id}', 'Admin\ExtensionQuizController@broadcast')
         ->name("admin_extension_quiz_broadcast");

    // BackendController
    Route::get('/change-pass', 'Admin\BackendController@changePassword')
        ->name('admin_change_password');
    Route::get('/logout', 'Admin\BackendController@logout')
        ->name('admin_logout');
    Route::get('/login', 'Admin\BackendController@login')
        ->name('admin_login');
    Route::post('/login', 'Admin\BackendController@validateLogin')
        ->name('admin_validate_login');
    Route::post('/save-pass', 'Admin\BackendController@saveNewPassword')
        ->name('admin_save_new_password');
    Route::get('/change-password/{type}', 'Admin\BackendController@forceChangePassword')
        ->name('admin_force_change_password');
    Route::post('/change-password', 'Admin\BackendController@saveChangedPassword')
        ->name('admin_save_changed_password');
    Route::get('/forgot-password', 'Admin\BackendController@forgotPassword')
        ->name('admin_forgot_password');
    Route::post('/forgot-password', 'Admin\BackendController@submitForgotPassword')
        ->name('admin_forgot_password_submit');
    Route::get('/resend-mail', 'Admin\BackendController@resendEmail')
        ->name('admin_resend_email');
    Route::get('/reset-password/{code}', 'Admin\BackendController@resetPassword')
        ->name('admin_reset_password');
    Route::post('/reset-password/{code}', 'Admin\BackendController@saveResetPassword')
        ->name('admin_save_reset_password');
    Route::post('/google/login/validate', 'Admin\BackendController@validateGoogleLogin')
        ->name('admin_validate_google_login');
});

Route::prefix("deploy")->middleware(["deploy"])->group(function (){

    // DeploymentController
    Route::get('/migrate', "Deploy\DeploymentController@migrate")
        ->name("deploy_migrate")->withoutMiddleware("web");
    Route::get('/rebuild_cache', "Deploy\DeploymentController@rebuildCache")
        ->name("deploy_rebuild_cache");
    Route::get('/create_first_user', "Deploy\DeploymentController@createFirstUser")
        ->name("deploy_create_first_user");
    Route::get('/maintenance', "Deploy\DeploymentController@maintenance")
        ->name("deploy_maintenance");
	Route::get('/add_maintenance_ip', "Deploy\DeploymentController@addMaintenanceIP")
		->name("deploy_add_ip");
	Route::get('/add_backend_ip', "Deploy\DeploymentController@addBackendIP")
		->name("deploy_add_backend_ip");
     Route::get('/revert_htaccess', "Deploy\DeploymentController@revertHtaccess")
        ->name("deploy_revert_htaccess");
     Route::get('/optimize_images', "Deploy\DeploymentController@optimizeImages")
        ->name("deploy_optimize_images");
     /*Route::get('/generate_webp', "Deploy\DeploymentController@generateWebp")
        ->name("deploy_generate_webp");*/
});

Route::middleware((throttleEnabled() ? ['throttle:600,1'] : []))->group(function () {
        
    // SiteController
    Route::get("/", 'Site\SiteController@home')
        ->name("site_home");
    Route::get("/dashboard", 'Site\SiteController@sitePage')
        ->name("site_dashboard");

    Route::match(['GET','POST'], '{page}', 'Site\SiteController@sitePage')
        ->where('page', '(dashboard|notifications|participate|participate-thank-you|faq|hardware-quiz|memory|guess-the-game|math-riddle|find-the-mistakes)')
        ->name('site_page');

    Route::post("/competition/participate", 'Site\SiteController@participate')
        ->name("site_competition_participate");

    Route::post("/submit-memory", 'Site\SiteController@memorySubmit')
        ->name("site_memory_points");
    Route::post("/submit-quiz", 'Site\SiteController@quizSubmit')
        ->name("site_quiz_points");
    Route::post("/submit-guess-the-game", 'Site\SiteController@guessTheGameSubmit')
        ->name("site_guess_the_game_points");
    Route::post("/submit-math-riddle", 'Site\SiteController@mathRiddleSubmit')
        ->name("site_math_riddle_points");
    Route::post("/submit-find-the-mistake", 'Site\SiteController@findTheMistakeSubmit')
        ->name("site_find_the_mistake_points");
    Route::post("code/redeem", 'Site\SiteController@redeemCode')
        ->name("site_redeem_code");

    Route::post('/save-push-token', 'Site\SiteController@savePushToken')
        ->name('site_save_push_token');
    Route::post('/remove-push-token', 'Site\SiteController@removePushToken')
        ->name('site_remove_push_token');

    Route::post('/save-stat', 'Site\SiteController@saveStat')
        ->name('site_save_stat');
    Route::get("/logout", 'Site\SiteController@logout')
        ->name("site_logout");

    // LoginController
    Route::get('/google/login', 'Site\LoginController@googleLogin')
        ->name('site_google_login');
    Route::get('/google/callback', 'Site\LoginController@googleCallback')
        ->name('site_google_callback');
    Route::get('/twitch/login', 'Site\LoginController@twitchLogin')
        ->name('site_twitch_login');
    Route::get('/twitch/callback', 'Site\LoginController@twitchCallback')
        ->name('site_twitch_callback');
    Route::get('/twitch/chat/login', 'Site\LoginController@twitchChatLogin')
        ->name('site_twitch_chat_login');
    Route::get('/twitch/chat/callback', 'Site\LoginController@twitchChatCallback')
        ->name('site_twitch_chat_callback');

    // PsnApiController
    Route::get('/psn-login', 'Site\PsnApiController@login')
        ->name('psn_login');
    Route::get('/token_auth', 'Site\PsnApiController@tokenAuth')
        ->name('token_auth');
});

Route::prefix("debug")->group(function () {

    // DebugController
    Route::get('/', "Site\DebugController@index")->name("debug_index");
    Route::post('/debug_login', "Site\DebugController@debugLogin")->name("debug_login");
    Route::get('/debug_action', "Site\DebugController@debugAction")->name("debug_action");
    //Route::get("/dummy-participants", 'Site\DebugController@dummyParticipantsData')->name("debug_dummy_participants");
});

// PWAController
Route::get('/offline', 'Site\PWAController@offlinePage')
    ->name('site_offline');
Route::get('/manifest.json', 'Site\PWAController@manifestJson')
    ->name('site_manifest');

// StreamController
Route::get("/stream/scroller", 'Stream\StreamController@scroller')
    ->name("stream_scroller");
Route::get("/stream/scroller-data", 'Stream\StreamController@scrollerData')
    ->name("stream_scroller_data");
Route::get("/stream/winner-screen/{type}/{code}", 'Stream\StreamController@winnerScreen')
    ->name("stream_winner_screen");
/*Route::post("/stream/winner-screen-data", 'Stream\StreamController@winnerScreenData')
    ->name("stream_winner_screen_data");
Route::post("/stream/notify-winners", 'Stream\StreamController@notifyWinners')
    ->name("stream_notify_winners");*/

Route::get("/stream/large-quiz-view", 'Stream\StreamController@streamLargeQuizView')
    ->name("stream_large_quiz_view");
Route::get("/stream/small-quiz-view", 'Stream\StreamController@streamSmallQuizView')
    ->name("stream_small_quiz_view");
Route::post("/stream/question-wrap-data", 'Stream\StreamController@questionWrapData')
    ->name("stream_question_wrap_data");
Route::get("/stream/generate-image", 'Stream\StreamController@generateImage')
    ->name("stream_generate_image");
Route::get("/stream/countdown-screen", 'Stream\StreamController@streamCountdownScreen')
    ->name("stream_countdown_screen");

// CronController
Route::get("/cron/draw-winner", 'Site\CronController@drawWinner')
    ->name('cron_draw_winner');
Route::get("/cron/site-live-push", 'Site\CronController@siteLivePush')
    ->name('cron_site_live_push');
Route::get("/cron/stream-live-push", 'Site\CronController@streamLivePush')
    ->name('cron_stream_live_push');
Route::get('/cron/sync-reward-codes', 'Site\CronController@syncRewardCodes')
    ->name('cron_sync_reward_codes');
Route::get("/cron/sync/products", 'Site\CronController@syncProducts')
    ->name('cron_sync_api_products');
Route::get('/cron/set-statistics-now', 'Site\CronController@setStatistics')
    ->name('cron_set_statistics');