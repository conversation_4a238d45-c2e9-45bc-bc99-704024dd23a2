/*
font-family: 'SST', Arial, sans-serif;
Light 300, Regular 400, Medium 500, Bold 700
*/

:root {
    scroll-behavior: auto;
    --cc-font-size: 14px;
    --cc-fs-height: 100vh;
}

@media (min-width: 1367px) {
    :root {
        --cc-font-size: 16px;
    }
}

@media (min-width: 1920px) {
    :root {
        --cc-font-size: 18px;
    }
}

:root {
    --text-1: 8px;
    --text-2: 10px;
    --text-3: 12px;
    --text-4: 14px;
    --text-5: 17px;
    --text-6: 20px;
    --text-7: 24px;
    --text-8: 29px;
    --text-9: 35px;
    --text-10: 42px;
    --text-11: 50px;
    --space-1: 2px;
    --space-2: 4px;
    --space-3: 7px;
    --space-4: 10px;
    --space-5: 14px;
    --space-6: 21px;
    --space-7: 28px;
    --space-8: 35px;
    --space-9: 42px;
    --space-10: 56px;
    --space-11: 70px;
    --space-12: 112px;
    --space-13: 224px;
    --color-1-light: 255 255 255;
    --color-1-dark: 18 19 20;
    --color-4-dark: 255 255 255;
    --color-10-dark: 83 177 255;
    --color-role-text-primary-base-dark: rgb(var(--color-4-dark)/1);
    --color-role-text-primary-base: var(--color-role-text-primary-base-dark);
    --color-role-page-backgrounds-primary: var(--color-role-page-backgrounds-primary-dark);
    --color-role-text-button-light: rgb(var(--color-1-light)/1);
    --color-role-page-backgrounds-primary-dark: rgb(var(--color-1-dark)/1);
    --color-role-text-link-base: var(--color-role-text-link-base-dark);
    --color-role-text-link-base-dark: rgb(var(--color-10-dark)/1);
    --icon-size-1: 12px;
    --icon-size-2: 18px;
    --icon-size-3: 28px;
    --icon-size-4: 40px;
    --icon-size-5: 64px;
    --icon-size-6: 96px;
    --sticker-size-1: 36px;
    --sticker-size-2: 42px;
    --sticker-size-3: 56px;
    --sticker-size-4: 70px;
    --sticker-size-5: 112px;
    --corner-size-0: 2px;
    --corner-size-1: 3px;
    --corner-size-2: 6px;
    --corner-size-3: 12px;
    --corner-size-4: 24px;
    --color-role-backgrounds-overlay-card-dark: rgb(var(--color-1-dark)/.8);
}

@media (min-width: 768px) {
    :root {
        --text-1: 8px;
        --text-2: 10px;
        --text-3: 12px;
        --text-4: 14px;
        --text-5: 18px;
        --text-6: 22px;
        --text-7: 27px;
        --text-8: 34px;
        --text-9: 43px;
        --text-10: 53px;
        --text-11: 67px;
    }
}

@media (min-width: 1367px) {
    :root {
        --text-1: 10px;
        --text-2: 12px;
        --text-3: 14px;
        --text-4: 16px;
        --text-5: 20px;
        --text-6: 25px;
        --text-7: 31px;
        --text-8: 39px;
        --text-9: 49px;
        --text-10: 61px;
        --text-11: 76px;
        --space-1: 2px;
        --space-2: 4px;
        --space-3: 8px;
        --space-4: 12px;
        --space-5: 16px;
        --space-6: 24px;
        --space-7: 32px;
        --space-8: 40px;
        --space-9: 48px;
        --space-10: 64px;
        --space-11: 80px;
        --space-12: 128px;
        --space-13: 256px;
        --icon-size-1: 14px;
        --icon-size-2: 22px;
        --icon-size-3: 34px;
        --icon-size-4: 48px;
        --icon-size-5: 72px;
        --icon-size-6: 108px;
        --sticker-size-1: 40px;
        --sticker-size-2: 48px;
        --sticker-size-3: 64px;
        --sticker-size-4: 80px;
        --sticker-size-5: 128px;
        --corner-size-0: 2px;
        --corner-size-1: 4px;
        --corner-size-2: 8px;
        --corner-size-3: 16px;
        --corner-size-4: 32px;
    }
}

@media (min-width: 1920px) {
    :root {
        --text-1: 10px;
        --text-2: 12px;
        --text-3: 14px;
        --text-4: 18px;
        --text-5: 22px;
        --text-6: 28px;
        --text-7: 36px;
        --text-8: 44px;
        --text-9: 54px;
        --text-10: 68px;
        --text-11: 86px;
        --space-1: 2px;
        --space-2: 5px;
        --space-3: 9px;
        --space-4: 14px;
        --space-5: 18px;
        --space-6: 27px;
        --space-7: 36px;
        --space-8: 45px;
        --space-9: 54px;
        --space-10: 72px;
        --space-11: 90px;
        --space-12: 144px;
        --space-13: 288px;
        --icon-size-1: 18px;
        --icon-size-2: 28px;
        --icon-size-3: 42px;
        --icon-size-4: 60px;
        --icon-size-5: 96px;
        --icon-size-6: 144px;
        --sticker-size-1: 46px;
        --sticker-size-2: 54px;
        --sticker-size-3: 72px;
        --sticker-size-4: 90px;
        --sticker-size-5: 144px;
        --corner-size-0: 3px;
        --corner-size-1: 6px;
        --corner-size-2: 12px;
        --corner-size-3: 24px;
        --corner-size-4: 48px;
    }
}

html {
    /* this is static var ( same value in all themes ) */
    --psin-font: 'SST', Arial, sans-serif;
    --psin-login-bg-static: #1b507a;
    --psin-black-static: #000;
    --psin-black-15p-static: rgba(0, 0, 0, 0.15);
    --psin-black-30p-static: rgba(0, 0, 0, 0.3);
    --psin-black-50p-static: rgba(0, 0, 0, 0.5);
    --psin-black-60p-static: rgba(0, 0, 0, 0.6);
    --psin-black-85p-static: rgba(0, 0, 0, 0.85);
    --psin-black-100-static: #0b0b0b;
    --psin-black-200-static: #1f1f1f;
    --psin-black-200-70p-static: rgba(31, 31, 31, 0.7);
    --psin-black-200-80p-static: rgba(31, 31, 31, 0.8);
    --psin-black-200-90p-static: rgba(31, 31, 31, 0.9);
    --psin-black-300-static: #202020;
    --psin-black-400-0p-static: rgba(70, 70, 70, 0);
    --psin-black-700-static: #767676;
    --psin-white-static: #fff;
    --psin-gray-static: #f5f5f5;
    --psin-gray-20-80p-static: rgba(242, 242, 242, 0.8);
    --psin-gray-30-static: #f3f3f3;
    --psin-gray-100-static: #f0f0f0;
    --psin-gray-100-30p-static: rgba(240, 240, 240, 0.3);
    --psin-gray-100-40p-static: rgba(240, 240, 240, 0.4);
    --psin-gray-200-static: #eee;
    --psin-gray-250-static: #e5e5e5;
    --psin-gray-280-static: #e8e8e8;
    --psin-gray-300-static: #d1d1d1;
    --psin-gray-400-static: #dedede;
    --psin-gray-400-22p-static: rgba(222, 222, 222, 0.22);
    --psin-gray-500-static: #c4c4c4;
    --psin-blue-static: #2d64e6;
    --psin-blue-active-static: #123b9b;
    --psin-blue-100-static: #01bbef;
    --psin-blue-150-40p-static: rgba(1, 120, 191, 0.4);
    --psin-blue-200-static: #0074bc;
    --psin-blue-300-static: #525492;
    --psin-blue-400-static: #020669;
    --psin-blue-500-static: #0074BC;
    --psin-red-static: #e62d2d;
    --psin-red-active-static: #b31c1c;
    --psin-green-static: #8bb21e;
    --psin-green-100-static: #54e59b;
    --psin-orange-static: #ef7301;
    --psin-orange-100-static: #ed5f2b;
    --psin-orange-200-static: #d53b00;
    --psin-orange-200-active-static: #6e2108;

    /* for light theme (default) */
    --psin-login-bg: #1b507a;
    --psin-black: #000;
    --psin-black-15p: rgba(0, 0, 0, 0.15);
    --psin-black-30p: rgba(0, 0, 0, 0.3);
    --psin-black-50p: rgba(0, 0, 0, 0.5);
    --psin-black-60p: rgba(0, 0, 0, 0.6);
    --psin-black-85p: rgba(0, 0, 0, 0.85);
    --psin-black-100: #0b0b0b;
    --psin-black-200: #1f1f1f;
    --psin-black-200-70p: rgba(31, 31, 31, 0.7);
    --psin-black-200-80p: rgba(31, 31, 31, 0.8);
    --psin-black-200-90p: rgba(31, 31, 31, 0.9);
    --psin-black-300: #202020;
    --psin-black-400-0p: rgba(70, 70, 70, 0);
    --psin-black-700: #767676;
    --psin-white: #fff;
    --psin-gray: #f5f5f5;
    --psin-gray-20-80p: rgba(242, 242, 242, 0.8);
    --psin-gray-30: #f3f3f3;
    --psin-gray-100: #f0f0f0;
    --psin-gray-100-30p: rgba(240, 240, 240, 0.3);
    --psin-gray-100-40p: rgba(240, 240, 240, 0.4);
    --psin-gray-200: #eee;
    --psin-gray-250: #e5e5e5;
    --psin-gray-280: #e8e8e8;
    --psin-gray-300: #d1d1d1;
    --psin-gray-400: #dedede;
    --psin-gray-400-22p: rgba(222, 222, 222, 0.22);
    --psin-gray-500: #c4c4c4;
    --psin-blue: #2d64e6;
    --psin-blue-active: #123b9b;
    --psin-blue-100: #01bbef;
    --psin-blue-150-40p: rgba(1, 120, 191, 0.4);
    --psin-blue-200: #0074bc;
    --psin-blue-300: #525492;
    --psin-blue-400: #020669;
    --psin-blue-500: #0074BC;
    --psin-red: #e62d2d;
    --psin-red-active: #b31c1c;
    --psin-green: #8bb21e;
    --psin-green-100: #54e59b;
    --psin-orange: #ef7301;
    --psin-orange-100: #ed5f2b;
    --psin-orange-200: #d53b00;
    --psin-orange-200-active: #6e2108;

    --psin-body-bg: var(--psin-white-static);
    --psin-sidebar-bg: var(--psin-gray-200-static);
    --psin-widget-bg: var(--psin-black-300);
    --psin-widget-border-color: var(--psin-gray-400);
}

/* for dark theme */
html.prefers-color-mode-dark {
    --psin-login-bg: #1b507a;
    --psin-black: #fff;
    --psin-black-15p: rgba(255, 255, 255, 0.15);
    --psin-black-30p: rgba(255, 255, 255, 0.3);
    --psin-black-50p: rgba(255, 255, 255, 0.5);
    --psin-black-60p: rgba(255, 255, 255, 0.6);
    --psin-black-85p: rgba(255, 255, 255, 0.85);
    --psin-black-100: #fbfbfb;
    --psin-black-200: #f1f1f1;
    --psin-black-200-70p: rgba(31, 31, 31, 0.7);
    --psin-black-200-80p: rgba(31, 31, 31, 0.8);
    --psin-black-200-90p: rgba(31, 31, 31, 0.9);
    --psin-black-300: #202020;
    --psin-black-400-0p: rgba(70, 70, 70, 0);
    --psin-black-700: #c6c6c6;
    --psin-white: #000;
    --psin-gray: #050505;
    --psin-gray-20-80p: rgba(62, 62, 62, 0.8);
    --psin-gray-30: #030303;
    --psin-gray-100: #0f0f0f;
    --psin-gray-100-30p: rgba(60, 60, 60, 0.3);
    --psin-gray-100-40p: rgba(60, 60, 60, 0.4);
    --psin-gray-200: #111;
    --psin-gray-250: #151515;
    --psin-gray-280: #181818;
    --psin-gray-300: #4b4b4b;
    --psin-gray-400: #292929;
    --psin-gray-400-22p: rgba(55, 55, 55, 0.22);
    --psin-gray-500: #525252;
    --psin-blue: #2d64e6;
    --psin-blue-active: #123b9b;
    --psin-blue-100: #01bbef;
    --psin-blue-150-40p: rgba(1, 120, 191, 0.4);
    --psin-blue-200: #0074bc;
    --psin-blue-300: #525492;
    --psin-blue-400: #020669;
    --psin-blue-500: #0074BC;
    --psin-red: #e62d2d;
    --psin-red-active: #b31c1c;
    --psin-green: #8bb21e;
    --psin-green-100: #54e59b;
    --psin-orange: #ef7301;
    --psin-orange-100: #ed5f2b;
    --psin-orange-200: #d53b00;
    --psin-orange-200-active: #6e2108;

    --psin-body-bg: var(--psin-black-200-static);
    --psin-sidebar-bg: var(--psin-black-100-static);
    --psin-widget-bg: #181818;
    --psin-widget-border-color: rgba(60, 60, 60, 0.3);
}


.no-script-msg{font:0.750em Arial, verdana, Helvetica, sans-serif;background:#FFFFCC url(../gfx/icon-noScript.gif) no-repeat 5px 10px; width: auto; padding: 0.625em 0.625em 0.625em 1.5em; margin: 0.5em; border: .1rem solid #CD3D18; font-weight: bold; height: auto; font-size: 11px; color:var(--psin-black); line-height: 1.5em; clear: both; }
input[type="text"], input[type="email"], input[type="password"],input[type="number"], input[type="tel"], input[type="button"],input[type="reset"],input[type="submit"], button{-webkit-font-smoothing:antialiased;-moz-font-smoothing:antialiased; font-family: var(--psin-font); font-weight: 400; -webkit-appearance: none; }
html { font-size: 10px; }
body { min-width: 320px; font-size: 1.5rem; line-height: 1.5; overflow-x: hidden; }
:focus-visible { outline: .2rem solid #fff; -webkit-box-shadow: 0 0 0 .2rem #0b1f42; box-shadow: 0 0 0 .2rem #0b1f42; border-radius: .8rem; outline-offset: 0.2rem; }

@media (min-width: 768px) {
    body { font-size: 1.6rem; }
}

@media (min-width: 1920px) {
    body { font-size: 1.8rem; }
}

@media (min-width: 2560px) {
    html { font-size: 12.5px; }
}

html,
body {background-color: #E9ECF4; color: #1f1f1f; position: relative; font-family: var(--psin-font); font-weight: 400; }

html.footer-show,
html.footer-show body { overflow: hidden; }

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button { -webkit-appearance: none; margin: 0; }
input[type=number] { -moz-appearance: none; -moz-appearance: textfield; margin: 0; }

a { color: var(--psin-blue); font-weight: normal; cursor: pointer; -webkit-transition: color 0.15s linear; -o-transition: color 0.15s linear; transition: color 0.15s linear; text-decoration: underline; outline: none; }
a:hover { color: var(--psin-blue); text-decoration: none; }
a.btn,
a.btn:hover { text-decoration: none; }

h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 { font-family: "sst",Arial,sans-serif; font-weight: 300; line-height: 1.25em; -webkit-margin-before: calc(var(--cc-font-size)*2); margin-block-start: calc(var(--cc-font-size)*2); -webkit-margin-after:  var(--cc-font-size); margin-block-end:  var(--cc-font-size); font-style: normal; }

h1,
.h1 { font-size: calc(var(--cc-font-size)*2.48832); }
h2,
.h2 { font-size: calc(var(--cc-font-size)*2.0736); }
h3,
.h3 { font-size: calc(var(--cc-font-size)*1.728); }
h4,
.h4 { font-size: calc(var(--cc-font-size)*1.44); }
h5,
.h5 { font-size: calc(var(--cc-font-size)*1.2); }
h6,
.h6 { font-size: var(--cc-font-size); }

@media (min-width: 768px) {
    h1,
    .h1 { font-size: calc(var(--cc-font-size)*3.05176); }
    h2,
    .h2 { font-size: calc(var(--cc-font-size)*2.44141); }
    h3,
    .h3 { font-size: calc(var(--cc-font-size)*1.95312); }
    h4,
    .h4 { font-size: calc(var(--cc-font-size)*1.5625); }
    h5,
    .h5 { font-size: calc(var(--cc-font-size)*1.25); }
    h6,
    .h6 { font-size: var(--cc-font-size); }
}


blockquote,
.blockquote { -webkit-margin-after:  var(--cc-font-size); margin-block-end:  var(--cc-font-size); font-size: calc(var(--cc-font-size)*1.25); }

p { -webkit-margin-after:  var(--cc-font-size); margin-block-end:  var(--cc-font-size); }

.p1 {font-size: calc(var(--cc-font-size)*1.2); }
.p2 { font-size: 1.5rem; }

.p1 > :last-child,
.p1 > :last-child {margin-bottom: 0;}

@media (min-width: 768px) {
    .p1 {font-size: calc(var(--cc-font-size)*1.25); }
}


ul, ol { display: block; margin: 0; -webkit-margin-after:  var(--cc-font-size); margin-block-end:  var(--cc-font-size); padding: 0; list-style: none; }
ul > li ,
ol > li { -webkit-padding-start: 1.714em; padding-inline-start: 1.714em; }

ul:first-child:not(.no-firstchild),
ol:first-child:not(.no-firstchild) { -webkit-margin-before: 0; margin-block-start: 0; }

ul:last-child:not(.no-lastchild),
ol:last-child:not(.no-lastchild) { -webkit-margin-after: 0; margin-block-end: 0; }

ul > li:last-child,
ol > li:last-child,
ul > li > :last-child,
ol > li > :last-child { -webkit-margin-after: 0; margin-block-end: 0; }

ul ul,
ul ol,
ol ol,
ol ul { margin: 0; }

li { position: relative; }

ul > li { display: block; list-style: none; }
ul > li::before { content: ""; display: block; position: absolute; top: 0.46em; left: 0.5em; width: 0.438em; height: 0.438em; background-color: #0068bd; border-radius: 0; }
.text-white ul > li::before,
ul.text-white > li::before { background-color: #fff; }
html[dir=rtl] ul > li::before { left: auto; right: -1.267em; }

ol { counter-reset: li; }
ol > li { display: block; list-style: none outside none; }
ol > li::before { content: counter(li, decimal)'.'; counter-increment: li; left: -0.5em; position: absolute; text-align: right; top: 0; width: 1.733em; color: #0068bd; font-weight: 400; }
.text-white ol > li::before,
ol.text-white > li::before { color: #fff; }
html[dir=rtl] ol > li::before { left: auto; right: -2.167em; text-align: left; }

dl { -webkit-margin-after: var(--cc-font-size); margin-block-end: var(--cc-font-size); }
dd { margin: 0; }

html select option { font-family: Arial, Helvetica, sans-serif; }

::-webkit-input-placeholder { color: #6B6B6B; }
::-moz-placeholder {color: #6B6B6B; }
:-ms-input-placeholder {color: #6B6B6B; }
.form-control::-moz-placeholder { color: #6B6B6B; }
.form-control::-webkit-input-placeholder { color: #6B6B6B; }
.form-control:-ms-input-placeholder { color: #6B6B6B; }
.form-control::-ms-input-placeholder { color: #6B6B6B; }
.form-control::placeholder { color: #6B6B6B; }

.mark,
mark { border-radius: 0.2rem; }


/*button*/
.btn-spacer{ font-size: 1em; line-height: normal;display: -webkit-box;display: -ms-flexbox;display: flex;-ms-flex-wrap: wrap;flex-wrap: wrap; }
.btn { position: relative; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; font-weight: 700; font-size: var(--text-4); line-height: 1.25; cursor: pointer; padding: 0.7rem 1.4rem; border: transparent solid 0; border-radius: calc(5rem*2); min-width: 1rem; text-align: center; -webkit-transition: all .1s ease-in-out; -o-transition: all .1s ease-in-out; transition: all .1s ease-in-out; margin: 0.4rem; text-transform: none; will-change: background-color, border-color, color; -webkit-box-shadow: none !important; box-shadow: none !important; outline: 0 !important; -webkit-border-radius: calc(5rem*2); -moz-border-radius: calc(5rem*2); -ms-border-radius: calc(5rem*2); -o-border-radius: calc(5rem*2); }
.btn-icon { padding: 0.7rem; }
.btn-border { padding: 0.5rem 1.2rem; }
.btn-border.btn-icon { padding: 0.5rem; }

.btn i { font-size: 2rem; }
.btn-text { min-height: 2rem; }


@media( min-width: 1367px ){
    .btn { padding: 0.9rem 1.6rem; }
    .btn-icon { padding: 0.9rem; }
    .btn-border { padding: 0.7rem 1.4rem; }
    .btn-border.btn-icon { padding: 0.7rem; }
    .btn i { font-size: 2.2rem; }
    .btn-text { min-height: 2.2rem; }
}

@media( min-width: 1920px ){
    .btn { padding: 1.1rem 1.8rem; }
    .btn-icon { padding: 1.1rem; }
    .btn-border { padding: 0.9rem 1.6rem; }
    .btn-border.btn-icon { padding: 0.9rem; }
    .btn i { font-size: 2.4rem; }
    .btn-text { min-height: 2.4rem; }
}


.btn-border { border-width: 0.2rem; }

.btn-uppercase { text-transform: uppercase; }
.btn-uppercase .btn-text { top: 0.125em;  }

.btn i { margin: 0 0.6rem; line-height: 1; }
.btn-text { line-height: 1; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; margin: 0 0.6rem; position: relative; }
.btn i + .btn-text,
.btn .btn-text + i { -webkit-margin-start: 0; margin-inline-start: 0; }

.btn > :first-child { -webkit-margin-start: 0; margin-inline-start: 0; }
.btn > :last-child { -webkit-margin-end: 0; margin-inline-end: 0; }


.btn:not(:disabled)::before,
.btn:not(.disabled)::before { outline: none; opacity: 0; border-radius: calc(5rem*2); display: block; content: ''; position: absolute; top: -0.2rem; left: -0.2rem; right: -0.2rem; bottom: -0.2rem; will-change: box-shadow, opacity; -webkit-transition: all .1s ease-in-out; -o-transition: all .1s ease-in-out; transition: all .1s ease-in-out; -webkit-border-radius: calc(5rem*2); -moz-border-radius: calc(5rem*2); -ms-border-radius: calc(5rem*2); -o-border-radius: calc(5rem*2); }

.btn:disabled,
.btn.disabled { opacity: 0.5; pointer-events: none; opacity: 1; background-color: #d1d1d1; border-color: #d1d1d1; }

.btn.no-radius { border-radius: 0; }
.btn.no-radius:not(:disabled)::before,
.btn.no-radius:not(.disabled)::before { border-radius: 0; }


.btn:hover::before,
.btn:focus::before,
.btn:active::before { opacity: 1; }


.btn-primary { background-color: #0070CC; color: #fff; border-color: transparent; }
.btn-primary:hover { background-color: #0064b7; color: #fff; border-color: transparent; }
.btn-primary:focus { background-color: #0059a3; color: #fff; border-color: transparent; }
.btn-primary:active { background-color: #0059a3 !important; color: #fff !important; border-color: transparent !important; }

.btn-primary::before,
.btn-primary:hover::before { -webkit-box-shadow: 0 0 0 0.2rem #0064b7; box-shadow: 0 0 0 0.2rem #0064b7; }
.btn-primary:focus::before { -webkit-box-shadow: 0 0 0 0.2rem #0059a3; box-shadow: 0 0 0 0.2rem #0059a3; }
.btn-primary:active::before { -webkit-box-shadow: 0 0 0 0.2rem #0059a3; box-shadow: 0 0 0 0.2rem #0059a3; }

.btn-primary:disabled,
.btn-primary.disabled { opacity: 0.5; background-color: #0070CC !important; border-color: #fff !important; }


.btn-white { background-color: #fff !important; color: #1f1f1f !important; border-color: transparent; }
.btn-white:hover { background-color: #e5e5e5 !important; color: #1f1f1f; border-color: transparent; }
.btn-white:focus { background-color: #ccc !important; color: #1f1f1f; border-color: transparent; }
.btn-white:active { background-color: #ccc !important; color: #1f1f1f; border-color: transparent; }

.btn-white::before,
.btn-white:hover::before { -webkit-box-shadow: 0 0 0 0.2rem #e5e5e5; box-shadow: 0 0 0 0.2rem #e5e5e5; }
.btn-white:focus::before { -webkit-box-shadow: 0 0 0 0.2rem #ccc; box-shadow: 0 0 0 0.2rem #ccc; }
.btn-white:active::before { -webkit-box-shadow: 0 0 0 0.2rem #ccc; box-shadow: 0 0 0 0.2rem #ccc; }

.btn-white:disabled,
.btn-white.disabled { opacity: 1; background-color: #d1d1d1 !important; border-color: #d1d1d1; }

.btn-white-outline {color: #fff;border: 0.2rem solid; border-color: #fff; background-color: transparent;}
.btn-white-outline:not(:disabled):not(.disabled):hover,
.btn-white-outline:not(:disabled):not(.disabled):focus {color: #1F1F1F; background-color: #e5e5e5; border-color: transparent;}
.btn-white-outline:not(:disabled):not(.disabled):active { color: #1F1F1F; background-color: #ccc; border-color: transparent; }

.btn-white-outline:not(.disabled)::before { top: -0.4rem; left: -0.4rem; right: -0.4rem; bottom: -0.4rem; }
.btn-white-outline:not(:disabled):not(.disabled):hover::before { -webkit-box-shadow: 0 0 0 0.2rem #fff; box-shadow: 0 0 0 0.2rem #fff; }
.btn-white-outline:not(:disabled):not(.disabled):focus::before { -webkit-box-shadow: 0 0 0 0.2rem #ccc; box-shadow: 0 0 0 0.2rem #ccc; }
.btn-white-outline:not(:disabled):not(.disabled):active::before { -webkit-box-shadow: 0 0 0 0.2rem #ccc; box-shadow: 0 0 0 0.2rem #ccc; }

.btn-white-outline.disabled, .btn-white-outline:disabled {color: #d1d1d1;border-color: #d1d1d1;background-color: transparent;}


.btn-primary-outline {color: #0070CC;border-color: #0070CC;background-color: transparent;}
.btn-primary-outline:not(:disabled):not(.disabled):hover {color: #fff; background-color: #0064b7; border-color: #0064b7;}
.btn-primary-outline:not(:disabled):not(.disabled):focus {color: #fff; background-color: #0059a3; border-color: #0059a3;}
.btn-primary-outline:not(:disabled):not(.disabled):hover::before {border-color: #0064b7;opacity: 1;}
.btn-primary-outline:not(:disabled):not(.disabled):focus::before {border-color: #0059a3;opacity: 1;}
.btn-primary-outline:not(:disabled):not(.disabled):active { color: #fff; background-color: #0059a3; border-color: #0059a3; }
.btn-primary-outline:not(:disabled):not(.disabled):active::before {border-color: #0059a3;}
.btn-primary-outline.disabled, .btn-primary-outline:disabled {color: #d1d1d1;border-color: #d1d1d1;background-color: transparent;}


.btn-secondary { background-color: #1f1f1f; color: #fff; border-color: transparent; }
.btn-secondary:focus { background-color: #1f1f1f; color: #fff; border-color: transparent; }
.btn-secondary:hover { background-color: #1f1f1f; color: #fff; border-color: transparent; }
.btn-secondary:active { background-color: #000 !important; color: #fff; border-color: transparent; }

.btn-secondary::before,
.btn-secondary:hover::before,
.btn-secondary:focus::before { -webkit-box-shadow: 0 0 0 0.2rem #1f1f1f; box-shadow: 0 0 0 0.2rem #1f1f1f; }
.btn-secondary:active::before { -webkit-box-shadow: 0 0 0 0.2rem #000; box-shadow: 0 0 0 0.2rem #000; }

.btn-secondary:disabled,
.btn-secondary.disabled { opacity: 1; background-color: #d1d1d1 !important; border-color: #d1d1d1; }


.btn-secondary2 { background-color: #363636; color: #fff; border-color: transparent; }
.btn-secondary2:focus { background-color: #363636; color: #fff; border-color: transparent; }
.btn-secondary2:hover { background-color: #363636; color: #fff; border-color: transparent; }
.btn-secondary2:active { background-color: #262626 !important; color: #fff; border-color: transparent; }

.btn-secondary2::before,
.btn-secondary2:hover::before,
.btn-secondary2:focus::before { -webkit-box-shadow: 0 0 0 0.2rem #363636; box-shadow: 0 0 0 0.2rem #363636; }
.btn-secondary2:active::before { -webkit-box-shadow: 0 0 0 0.2rem #262626; box-shadow: 0 0 0 0.2rem #262626; }

.btn-secondary2:disabled,
.btn-secondary2.disabled { opacity: 1; background-color: #d1d1d1 !important; border-color: #d1d1d1; }

.btn-secondary-outline {color: #1f1f1f;border: 0.2rem solid;border-color: #1f1f1f;background-color: transparent;}
.btn-secondary-outline:not(:disabled):not(.disabled):hover,
.btn-secondary-outline:not(:disabled):not(.disabled):focus {color: #fff; background-color: #1f1f1f; border-color: #1f1f1f;}
.btn-secondary-outline:not(:disabled):not(.disabled):hover::before,
.btn-secondary-outline:not(:disabled):not(.disabled):focus::before {border-color: #1f1f1f;opacity: 1;}
.btn-secondary-outline:not(:disabled):not(.disabled):active { color: #fff; background-color: #000; border-color: #000; }
.btn-secondary-outline:not(:disabled):not(.disabled):active::before {border-color: #000;}

.btn-secondary-outline:not(.disabled)::before { top: -0.4rem; left: -0.4rem; right: -0.4rem; bottom: -0.4rem; }
.btn-secondary-outline:not(:disabled):not(.disabled):hover::before { -webkit-box-shadow: 0 0 0 0.2rem #1f1f1f; box-shadow: 0 0 0 0.2rem #1f1f1f; }
.btn-secondary-outline:not(:disabled):not(.disabled):focus::before { -webkit-box-shadow: 0 0 0 0.2rem #000; box-shadow: 0 0 0 0.2rem #000; }
.btn-secondary-outline:not(:disabled):not(.disabled):active::before { -webkit-box-shadow: 0 0 0 0.2rem #000; box-shadow: 0 0 0 0.2rem #000; }

.btn-secondary-outline.disabled, .btn-secondary-outline:disabled {color: #d1d1d1;border-color: #d1d1d1;background-color: transparent;}

.btn-danger { background-color: #e62d2d; color: #fff; border-color: transparent; }
.btn-danger:focus { background-color: #e62d2d; color: #fff; border-color: transparent; }
.btn-danger:hover { background-color: #e62d2d; color: #fff; border-color: transparent; }
.btn-danger:active { background-color: #b31c1c; color: #fff; border-color: transparent; }

.btn-danger::before,
.btn-danger:hover::before,
.btn-danger:focus::before { -webkit-box-shadow: 0 0 0 0.2rem #e62d2d; box-shadow: 0 0 0 0.2rem #e62d2d; }
.btn-danger:active::before { -webkit-box-shadow: 0 0 0 0.2rem #b31c1c; box-shadow: 0 0 0 0.2rem #b31c1c; }

.btn-danger:disabled,
.btn-danger.disabled { opacity: 1; background-color: #d1d1d1; border-color: #d1d1d1; }


.btn-cta { background-color: #d53b00; color: #fff; border-color: transparent; }
.btn-cta:hover { background-color: #c03500; color: #fff; border-color: transparent; }
.btn-cta:focus { background-color: #aa2f00; color: #fff; border-color: transparent; }
.btn-cta:active { background-color: #aa2f00 !important; color: #fff !important; border-color: transparent !important; }

.btn-cta::before,
.btn-cta:hover::before { -webkit-box-shadow: 0 0 0 2px #c03500; box-shadow: 0 0 0 2px #c03500; }
.btn-cta:focus::before { -webkit-box-shadow: 0 0 0 2px #aa2f00; box-shadow: 0 0 0 2px #aa2f00; }
.btn-cta:active::before { -webkit-box-shadow: 0 0 0 2px #aa2f00; box-shadow: 0 0 0 2px #aa2f00; }

.btn-cta:disabled,
.btn-cta.disabled { opacity: 0.5; background-color: #d1d1d1; border-color: #d1d1d1; }

.btn-black { background-color: #000; color: #fff; border-color: transparent; }
.btn-black:focus { background-color: #000; color: #fff; border-color: transparent; }
.btn-black:hover { background-color: #000; color: #fff; border-color: transparent; }
.btn-black:active { background-color: #000; color: #fff; border-color: transparent; }

.btn-black::before,
.btn-black:hover::before,
.btn-black:focus::before { -webkit-box-shadow: 0 0 0 0.2rem #000; box-shadow: 0 0 0 0.2rem #000; }
.btn-black:active::before { -webkit-box-shadow: 0 0 0 0.2rem #000; box-shadow: 0 0 0 0.2rem #000; }

.btn-twitch { background-color: #7c3cdd; color: #fff; border-color: transparent; }
.btn-twitch:hover { background-color: #9556f3; color: #fff; border-color: transparent; }
.btn-twitch:focus { background-color: #752edd; color: #fff; border-color: transparent; }
.btn-twitch:active { background-color: #752edd !important; color: #fff !important; border-color: transparent !important; }

.btn-twitch::before,
.btn-twitch:hover::before { -webkit-box-shadow: 0 0 0 0.2rem #9556f3; box-shadow: 0 0 0 0.2rem #9556f3; }
.btn-twitch:focus::before { -webkit-box-shadow: 0 0 0 0.2rem #752edd; box-shadow: 0 0 0 0.2rem #752edd; }
.btn-twitch:active::before { -webkit-box-shadow: 0 0 0 0.2rem #752edd; box-shadow: 0 0 0 0.2rem #752edd; }

.btn-twitch:disabled,
.btn-twitch.disabled { opacity: 0.5; background-color: #7c3cdd !important; border-color: #fff !important; }


[class*="cc_icon-"] { font-size: 1em; width: 1em; max-width: 1em; -webkit-box-flex: 0; -ms-flex: 0 0 1em; flex: 0 0 1em; display: block; position: relative; }
[class*="cc_icon-"]::before { content: ""; display: block; padding-top: 100%; position: relative; width: 100%; z-index: 1;}
[class*="cc_icon-"] svg,
[class*="cc_icon-"] img { display: block; width: 100%; height: 100%; position: absolute; top: 0; left: 0; z-index: 0; }
.cc_icon-ps-controller::before { padding-top: 93.33333333333333%; }
.cc_icon-upload::before { padding-top: 80%; }

.skip-content { margin: 0; position: absolute; top: 55px; left: 10px; z-index: 1000; -webkit-transform: translateY(-200%); -ms-transform: translateY(-200%); transform: translateY(-200%); -webkit-transition: -webkit-transform 0.2s ease-in-out; transition: -webkit-transform 0.2s ease-in-out; -o-transition: transform 0.2s ease-in-out; transition: transform 0.2s ease-in-out; transition: transform 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out; }
.skip-content:focus { -webkit-transform: translateY(0); -ms-transform: translateY(0); transform: translateY(0); }

@media (min-width: 768px) {
    .skip-content { top: 10px; left: 10px; }
}

@media (min-width: 1024px) {
    .skip-content { top: 52px; left: 80px; }
}

@media (min-width: 1440px) {
    .skip-content { top: 45px; left: 80px; }
}


.layout { position: relative; z-index: 1;}
.container { padding-left: 1.5rem; padding-right: 1.5rem; max-width: 916px; }
.row { --bs-gutter-x: 3rem }

.cc-section { position: relative; padding: 4.4rem 0; z-index: 0; width: 100%; }
.cc-section-inner { position: relative; z-index: 2; width: 100%; -webkit-box-flex: 0; -ms-flex: 0 0 100%; flex: 0 0 100%; max-width: 100%; }
.cc-section-bg { position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 0; display: block; }
.cc-section-bg picture,
.cc-section-bg iframe,
.cc-section-bg video,
.cc-section-bg img { display: block; width: 100%; height: 100%; -o-object-fit: cover; object-fit: cover; }

@media (min-width: 992px) {
    .cc-section { padding: 6rem 0; }
}

@media (min-width: 1200px) {
    .cc-section { padding: 8rem 0; }
}

.cc_imgbox { position: relative; display:block; }
.cc_imgbox::before { padding-top: 56.25%; width:100%; display:block; content:''; }
.cc_imgbox img { display: block; width: 100%; height: 100%; top: 0; left: 0; position: absolute; z-index: 1; -o-object-fit: cover; object-fit: cover; }
.cc_imgbox iframe,
.cc_imgbox video { display: block; width: 100%; height: 100%; top: 0; left: 0; position: absolute; z-index: 1; border-radius: 1.5rem; }

.ratio img,
.ratio video,
.ratio iframe { display: block; width: 100%; height: 100%; top: 0; left: 0; position: absolute; z-index: 1; -o-object-fit: cover; object-fit: cover; }
.ratio.contain img,
.ratio.contain video {-o-object-fit: contain;object-fit: contain;}
.ratio.static { width: 100%; height: 100%; position: relative; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; }
.ratio.static::before { display: none; }
.ratio.static img { position: static; width: auto; height: auto; max-width: 100%; max-height: 100%; }

.zoom_img { overflow:hidden; }
.zoom_img > img { -webkit-transition: 0.5s all ease-in-out; -o-transition: 0.5s all ease-in-out; transition: 0.5s all ease-in-out; }
.zoom_img:hover > img, .zoom_img:focus > img { -webkit-transform: scale(1.07); -ms-transform: scale(1.07); transform: scale(1.07); }

@media (min-width: 768px) {
    .container { max-width: 650px; }
}

@media (min-width: 992px) {
    .container { max-width: 916px; }
}

@media (min-width: 1367px) {
    .container { max-width: 1350px; }
}

@media (min-width: 1920px) {
    .container { max-width: 1734px; }
}


/* sony-bar */
.sony-bar { padding: 0; height: 36px; width: 100%; min-width: 320px; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-pack: end; -ms-flex-pack: end; justify-content: flex-end; z-index: 1; background-color: #000; overflow: hidden; }
.sony-bar a { display: block; color: #fff; }
.sony-bar a:focus-visible { outline-offset: -0.4rem; }
.sony-logo { display: block; height: 100%; width: 10.5rem; background-image: url(../gfx/sony_logo-r.svg); background-repeat: no-repeat; background-position: center center; margin: 0 8px; }



/* Footer */
html .gdk .footer-v2 {
    --dk-font: "sst",Arial,sans-serif;
    --dk-font-condensed: "sst condensed","sst",Arial,sans-serif;
    --dk-font-bebas-neue: "bebas neue","sst",Arial,sans-serif;
    --dk-font-mont: "Mont W05","sst",Arial,sans-serif
}

html[lang^=zh-hans] .gdk .footer-v2 {
    --dk-font: "PingFang SC","Hiragino Sans GB","Microsoft YaHei","WenQuanYi Micro Hei",Arial,sans-serif;
    --dk-font-condensed: "PingFang SC","Hiragino Sans GB","Microsoft YaHei","WenQuanYi Micro Hei",Arial,sans-serif;
    --dk-font-bebas-neue: "bebas neue","PingFang SC","Hiragino Sans GB","Microsoft YaHei","WenQuanYi Micro Hei",Arial,sans-serif
}

html[lang^=zh-hant] .gdk .footer-v2 {
    --dk-font: "å¾®è»Ÿæ­£é»‘é«”","Microsoft JhengHei","PMingLiu","æ–°ç´°æ˜Žé«”",sans-serif;
    --dk-font-condensed: "å¾®è»Ÿæ­£é»‘é«”","Microsoft JhengHei","PMingLiu","æ–°ç´°æ˜Žé«”",-apple-system,blinkmacsystemfont,sans-serif;
    --dk-font-bebas-neue: "bebas neue","å¾®è»Ÿæ­£é»‘é«”","Microsoft JhengHei","PMingLiu","æ–°ç´°æ˜Žé«”",-apple-system,blinkmacsystemfont,sans-serif
}

html[lang=ja-JP] .gdk .footer-v2,
html[lang=th-TH] .gdk .footer-v2 {
    --dk-font: "Hiragino Kaku Gothic Pro",Meiryo,Osaka,"MS PGothic",Arial,sans-serif;
    --dk-font-condensed: "Hiragino Kaku Gothic Pro",Meiryo,Osaka,"MS PGothic",Arial,sans-serif;
    --dk-font-bebas-neue: "bebas neue","Hiragino Kaku Gothic Pro",Meiryo,Osaka,"MS PGothic",Arial,sans-serif
}

html[lang=ko-KR] .gdk .footer-v2 {
    --dk-font: "Nanum Gothic","ë§‘ì€ê³ ë”•",Malgun Gothic,"ë‹ì›€",dotum,AppleGothic,Arial,sans-serif;
    --dk-font-condensed: "Nanum Gothic","ë§‘ì€ê³ ë”•",Malgun Gothic,"ë‹ì›€",dotum,AppleGothic,Arial,sans-serif;
    --dk-font-bebas-neue: "bebas neue","Nanum Gothic","ë§‘ì€ê³ ë”•",Malgun Gothic,"ë‹ì›€",dotum,AppleGothic,Arial,sans-serif
}


body,html {
    overflow-x: clip
}

body {
    position: relative;
    margin: 0;
}

.gdk *, html {
    -webkit-font-smoothing: antialiased
}

.gdk .footer-v2 {
    margin: 0;
    -moz-osx-font-smoothing: grayscale;
    color: #1f1f1f;
    font-size: 15px;
    font-family: var(--dk-font);
    font-style: normal;
    font-weight: 400;
}

.gdk {
    position: relative; z-index: 0;
}
.gdk * {
    line-height: 1.5em;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box
}

.gdk div.pagebanner,
.gdk div.section {
    line-height: 0
}

.gdk a {
    text-decoration: none;
    background-color: transparent
}

.gdk [hidden] {
    display: none
}

svg:not(:root) {
    overflow: hidden
}


.gdk .site-footer-v2 a:focus:not(.focus-visible),.gdk .site-footer-v2  button:focus:not(.focus-visible) {
    border-radius: 0;
    outline-offset: 0.125em;
    -webkit-box-shadow: none !important;
            box-shadow: none !important;
    outline: 2px solid var(--color-role-text-primary-base);
    outline-color: inherit;
}

.gdk .site-footer-v2  a:focus:not(:focus-visible),
.gdk .site-footer-v2  button:focus:not(:focus-visible) {
    outline: none;
}

.gdk .footer-v2 { font-size: 15px; }

@media (min-width: 768px) {
    .gdk .footer-v2 { font-size: 16px; }
}

@media (min-width: 1920px) {
    .gdk .footer-v2 { font-size: 18px; }
}

.gdk .theme--dark { color: var(--color-role-text-primary-base); }
.gdk .theme--dark { background-color: var(--color-role-page-backgrounds-primary); }
.gdk .theme--dark a { color: var(--color-role-text-link-base); }
.gdk .box a { color: var(--color-role-text-link-base); }
.gdk .theme--dark.theme--dark a { color: var(--color-role-text-primary-base); }
.gdk .site-footer-v2 { min-width: 240px;-webkit-padding-before: var(--space-6);padding-block-start: var(--space-6);-webkit-padding-after: var(--space-5);padding-block-end: var(--space-5);overflow: hidden;position: relative;background: #00439c; }
.gdk .site-footer-v2, .gdk .site-footer-v2 a { color: var(--color-role-text-button-light); }
.gdk .site-footer-v2 a:hover { opacity: .7; }
.gdk .site-footer-v2 a:active { opacity: .6; }
.gdk .grid { display: -ms-grid; display: grid;gap: var(--space-7) var(--space-5);-webkit-margin-after: var(--space-10);margin-block-end: var(--space-10);position: relative;z-index: 50;-ms-grid-columns: 1fr var(--space-5) 1fr var(--space-5) 1fr var(--space-5) 1fr;grid-template-columns: repeat(4,1fr);margin-left: var(--space-6);margin-right: var(--space-6); }

.gdk .site-footer-v2 .grid { margin-top: 0;margin-bottom: 0;row-gap: 0; }

.gdk .site-footer-v2 .grid { position: relative; }

.gdk .box { position: relative;z-index: 29;display: -ms-flexbox;display: -webkit-box;display: flex;-ms-flex-direction: column;-webkit-box-orient: vertical;-webkit-box-direction: normal;flex-direction: column; }
.gdk .layout__1--a>* { grid-column: 1/-1; }
.gdk .site-footer-v2__brand-area { -webkit-padding-after: var(--space-5);padding-block-end: var(--space-5);position: relative; }

.gdk .site-footer-v2 .grid::after { content: '';bottom: 0;height: 1px;width: 100%;margin: 0 auto;position: absolute;background: rgba(255,255,255,.2); }
.gdk .site-footer-v2 .grid:last-child::after { display:none; }

.gdk .site-footer-v2 .grid .box:only-child { -webkit-padding-after: var(--space-6);padding-block-end: var(--space-6); }

.gdk .site-footer-v2__ps-logo { display: -ms-flexbox;display: -webkit-box;display: flex; }
.gdk .box>div { z-index: 29; }
.gdk .box>div:last-child { -webkit-margin-after: 0;margin-block-end: 0; }
.gdk .site-footer-v2 svg { fill: var(--color-role-text-primary-base-dark); }
.gdk .site-footer-v2 .nav-accordion__icon svg { width: 100%;height: 100%; }
.gdk .site-footer-v2__logo svg { height: var(--sticker-size-4);width: auto; }
.gdk .site-footer-v2__ps-logo svg { height: var(--space-9); }
.gdk .box>div:last-child>* { -webkit-margin-after: 0;margin-block-end: 0; }
.gdk .site-footer-v2__categories::after { content: '';bottom: 0;height: 1px;width: 100%;margin: 0 auto;position: absolute;background: rgba(255,255,255,.2); }
.gdk .site-footer-v2__categories li a { color: var(--color-role-text-button-light);-webkit-hyphens: auto;-ms-hyphens: auto;hyphens: auto;cursor: pointer;text-decoration: none;word-break: break-word; }
.gdk .site-footer-v2__categories .nav-list .social-links a { height: var(--icon-size-3);width: var(--icon-size-3);-webkit-margin-end: var(--space-2);margin-inline-end: var(--space-2);margin-top: 0;margin-bottom: 0;display: inline-block; }
.gdk .site-footer-v2__categories .nav-accordion:hover { opacity: .7; }

@media (min-width: 360px) {
    .gdk .grid { -ms-grid-columns: (1fr)[4]; grid-template-columns: repeat(4,1fr);margin-left: var(--space-7);margin-right: var(--space-7); }
}

@media (orientation: landscape) {
    .gdk .grid { -ms-grid-columns: (1fr)[12]; grid-template-columns: repeat(12,1fr);margin-left: var(--space-9);margin-right: var(--space-9); }
}

@media (min-width: 768px) {
    .gdk .grid { -ms-grid-columns: (1fr)[12]; grid-template-columns: repeat(12,1fr);margin-left: var(--space-9);margin-right: var(--space-9); }
    .gdk .site-footer-v2__categories::after { display: none; }
}

@media (min-width: 768px), (orientation: landscape) {
    .gdk .site-footer-v2 .site-footer-v2__categories .nav-accordion:active,
    .gdk .site-footer-v2 .site-footer-v2__categories .nav-accordion:hover { opacity: 1; }
    .gdk .site-footer-v2 .site-footer-v2__categories a:focus { opacity: 0.7; }

}

@media (min-width: 1024px) {
    .gdk .grid { -ms-grid-columns: (1fr)[12]; grid-template-columns: repeat(12,1fr);margin-left: var(--space-9);margin-right: var(--space-9); }
}

@media (min-width: 1367px) {
    .gdk .grid { -ms-grid-columns: (1fr)[12]; grid-template-columns: repeat(12,1fr);margin-left: var(--space-11);margin-right: var(--space-11); }
}

@media (min-width: 1920px) {
    .gdk .grid { -ms-grid-columns: (1fr)[24]; grid-template-columns: repeat(24,1fr);max-width: 1920px;margin-left: var(--space-11);margin-right: var(--space-11); }
}

@media (min-width: 2120px) {
    .gdk .grid { margin-left: auto; margin-right: auto; }
}

.gdk .site-footer-v2__container { -webkit-padding-before: var(--space-4);padding-block-start: var(--space-4); }
.gdk .site-footer-v2 .grid.site-footer-v2__container { -ms-grid-rows: auto;grid-template-rows: auto;row-gap: var(--space-4); }
.gdk .site-footer-v2__categories { -webkit-padding-after: var(--space-4);padding-block-end: var(--space-4); }
.gdk .site-footer-v2__categories { position: relative; }
.gdk .site-footer-v2 .grid .\+links,
.gdk .site-footer-v2 .grid .\+social { grid-column: 1/-1; }

.gdk .txt-style-subtitle-bold { font-size: var(--text-3);font-weight: 600;line-height: 1.5em;--type-margin-top: var(--space-5);--type-margin-bottom: var(--space-5);-webkit-margin-before: var(--type-margin-top);margin-block-start: var(--type-margin-top);-webkit-margin-after: var(--type-margin-bottom);margin-block-end: var(--type-margin-bottom);color: var(--type-custom-color,var(--type-style-color));--type-style-color: var(--color-role-text-primary-base); }
.gdk .txt-style-subtitle-bold--m-0 { --type-margin-top: 0;--type-margin-bottom: 0; }
.gdk .site-footer-v2 .nav-accordion { font-size: var(--text-4);padding: 0;color: var(--color-role-text-button-light);width: 100%;border: none;display: -ms-flexbox;display: -webkit-box;display: flex;cursor: pointer;text-align: left;-ms-flex-direction: row;-webkit-box-orient: horizontal;-webkit-box-direction: normal;flex-direction: row;-ms-flex-align: center;-webkit-box-align: center;align-items: center;background: 0 0;font-family: var(--dk-font);-ms-flex-pack: justify;-webkit-box-pack: justify;justify-content: space-between; }
.gdk .site-footer-v2 .nav-accordion__icon { height: var(--icon-size-2);width: var(--icon-size-2);display: block;pointer-events: none;-webkit-transition: -webkit-transform .5s;transition: -webkit-transform .5s;-o-transition: transform .5s;transition: transform .5s;transition: transform .5s, -webkit-transform .5s; }

.gdk .txt-style-utility { font-size: var(--text-2);font-weight: 400;line-height: 1.5em;--type-margin-top: var(--space-3);--type-margin-bottom: var(--space-3);-webkit-margin-before: var(--type-margin-top);margin-block-start: var(--type-margin-top);-webkit-margin-after: var(--type-margin-bottom);margin-block-end: var(--type-margin-bottom);color: var(--type-custom-color,var(--type-style-color));--type-style-color: var(--color-role-text-primary-base); }
.gdk .site-footer-v2__categories .nav-list { font-size: var(--text-3);-webkit-margin-before: var(--space-3);margin-block-start: var(--space-3); }
.gdk ul, .gdk li { margin: 0; }
.gdk .site-footer-v2__categories ul { padding: 0;list-style: none; }
.gdk .site-footer-v2__categories li { margin: 0;padding: 0;-webkit-padding-after: var(--space-3);padding-block-end: var(--space-3); }

@media (max-width: 767px) {
    .txt-style-utility.nav-list { display:none; }
    .grid.site-footer-v2__container .box.site-footer-v2__categories:first-child .txt-style-utility.nav-list { display:block; }
    .gdk .site-footer-v2 .nav-accordion .nav-accordion__icon.ps-utility-plus { -webkit-transition:all 0.5s ease; -o-transition:all 0.5s ease; transition:all 0.5s ease; }
    .gdk .site-footer-v2 .nav-accordion.icon-rotate .nav-accordion__icon.ps-utility-plus { -webkit-transform:rotate(45deg); -ms-transform:rotate(45deg); transform:rotate(45deg); }
}

@media (min-width: 768px), (orientation: landscape) {
    .gdk .site-footer-v2__container { padding-top: var(--space-5); padding-bottom: var(--space-5); }
    .gdk .site-footer-v2__categories { -webkit-padding-after: 0;padding-block-end: 0; }
    .gdk .site-footer-v2 .nav-accordion { cursor: default; }
}

@media (min-width: 768px) {
    .gdk .site-footer-v2 .grid.site-footer-v2__container { -ms-grid-columns: (1fr)[3];grid-template-columns: repeat(3,1fr);-ms-grid-rows: 1fr var(--space-5) 1fr;grid-template-rows: repeat(2,1fr);row-gap: var(--space-5); }
    .gdk .site-footer-v2 .grid.site-footer-v2__container > *:nth-child(1) { -ms-grid-row: 1;-ms-grid-column: 1; }
    .gdk .site-footer-v2 .grid.site-footer-v2__container > *:nth-child(2) { -ms-grid-row: 1;-ms-grid-column: 2; }
    .gdk .site-footer-v2 .grid.site-footer-v2__container > *:nth-child(3) { -ms-grid-row: 1;-ms-grid-column: 3; }
    .gdk .site-footer-v2 .grid.site-footer-v2__container > *:nth-child(4) { -ms-grid-row: 3;-ms-grid-column: 1; }
    .gdk .site-footer-v2 .grid.site-footer-v2__container > *:nth-child(5) { -ms-grid-row: 3;-ms-grid-column: 2; }
    .gdk .site-footer-v2 .grid.site-footer-v2__container > *:nth-child(6) { -ms-grid-row: 3;-ms-grid-column: 3; }

    .gdk .site-footer-v2 .grid .\+links,
    .gdk .site-footer-v2 .grid .\+social { grid-column: auto/span 1; }
    .gdk .site-footer-v2 .grid .\+links:nth-child(1),
    .gdk .site-footer-v2 .grid .\+links:nth-child(2),
    .gdk .site-footer-v2 .grid .\+links:nth-child(3) { -ms-grid-row: 1; grid-row: 1; }
    .gdk .site-footer-v2 .grid .\+links:nth-child(4),
    .gdk .site-footer-v2 .grid .\+links:nth-child(5) { -ms-grid-row: 2; grid-row: 2; }
    .gdk .site-footer-v2 .grid .\+social:nth-child(6) { -ms-grid-row: 2; grid-row: 2; }

    .gdk .site-footer-v2 .nav-accordion__icon { display: none; }

    /* 16-08-2023 */
    .gdk .site-footer-v2__categories .nav-list { display: block !important; }

}

@media (min-width: 1024px) {
    .gdk .site-footer-v2 .grid.site-footer-v2__container { -ms-grid-columns: (1fr)[6];grid-template-columns: repeat(6,1fr);-ms-grid-rows: none;grid-template-rows: none; }
    .gdk .site-footer-v2 .grid.site-footer-v2__container > *:nth-child(1) { -ms-grid-row: 1;-ms-grid-column: 1; }
    .gdk .site-footer-v2 .grid.site-footer-v2__container > *:nth-child(2) { -ms-grid-row: 1;-ms-grid-column: 2; }
    .gdk .site-footer-v2 .grid.site-footer-v2__container > *:nth-child(3) { -ms-grid-row: 1;-ms-grid-column: 3; }
    .gdk .site-footer-v2 .grid.site-footer-v2__container > *:nth-child(4) { -ms-grid-row: 1;-ms-grid-column: 4; }
    .gdk .site-footer-v2 .grid.site-footer-v2__container > *:nth-child(5) { -ms-grid-row: 1;-ms-grid-column: 5; }
    .gdk .site-footer-v2 .grid.site-footer-v2__container > *:nth-child(6) { -ms-grid-row: 1;-ms-grid-column: 6; }
    .gdk .site-footer-v2 .grid .\+links:nth-child(4),
    .gdk .site-footer-v2 .grid .\+links:nth-child(5) { grid-row: auto; }
    .gdk .site-footer-v2 .grid .\+social { -ms-grid-column: 6; grid-column-start: 6; }
    .gdk .site-footer-v2 .grid .\+social:nth-child(6) { grid-row: auto; }
}

.gdk .site-footer-v2__logos.grid { padding-top: var(--space-5);padding-bottom: var(--space-5);display: -ms-flexbox;display: -webkit-box;display: flex;-ms-flex-direction: row-reverse;-webkit-box-orient: horizontal;-webkit-box-direction: reverse;flex-direction: row-reverse;-ms-flex-pack: justify;-webkit-box-pack: justify;justify-content: space-between; }
.gdk .media-block--image { --custom-mobile-width: 100%;--custom-tablet-width: 100%;--custom-desktop-width: 100%;width: var(--custom-mobile-width); }
.gdk .media-block { display: block;position: relative;--icon-block-theme: var(--color-role-text-primary-base-dark);margin: 0;-webkit-margin-after: var(--space-7);margin-block-end: var(--space-7); }
.gdk .site-footer-v2__badges .media-block { -webkit-margin-after: 0;margin-block-end: 0; }
.gdk .media-block__inner { position: relative; }
.gdk figure {margin: 0;}
.gdk .media-block__figure { width: 100%;overflow: hidden;position: relative;margin: 0;display: -ms-flexbox;display: -webkit-box;display: flex; }
.gdk .lozad { will-change: height,width,filter; }
.gdk .media-block__figure picture{ width: 100%;height: 100%;position: relative; }
.gdk .lozad[data-loaded=true] { -webkit-animation-name: fadeIn;animation-name: fadeIn;-webkit-animation-iteration-count: 1;animation-iteration-count: 1;-webkit-animation-duration: var(--animation-duration,.75s);animation-duration: var(--animation-duration,.75s);-webkit-animation-delay: 0s;animation-delay: 0s;-webkit-animation-timing-function: ease;animation-timing-function: ease;-webkit-animation-fill-mode: both;animation-fill-mode: both;-webkit-backface-visibility: hidden;backface-visibility: hidden;-webkit-animation-direction: normal;animation-direction: normal; }

.gdk .site-footer-v2__logo { display: -ms-flexbox;display: -webkit-box;display: flex;-ms-flex-direction: column;-webkit-box-orient: vertical;-webkit-box-direction: normal;flex-direction: column;-ms-flex-align: start;-webkit-box-align: start;align-items: flex-start; }
.gdk .site-footer-v2__copyright>* { font-size: var(--text-3);margin: 0; }

@media (min-width: 768px), (orientation: landscape) {
.gdk .media-block--image { width: var(--custom-tablet-width); }
.gdk .site-footer-v2__badges .media-block__figure { -ms-flex-pack: end;-webkit-box-pack: end;justify-content: flex-end; }
}

@media (min-width: 1024px) {
    .gdk .media-block--image { width: var(--custom-desktop-width); }
}

@-webkit-keyframes fadeIn {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

.gdk .site-footer-v2__country { padding-left: 0;padding-right: 0;-webkit-padding-before: var(--space-5);padding-block-start: var(--space-5);-webkit-padding-after: var(--space-4);padding-block-end: var(--space-4);border: none;display: -ms-flexbox;display: -webkit-box;display: flex;-ms-flex-direction: column;-webkit-box-orient: vertical;-webkit-box-direction: normal;flex-direction: column;background: 0 0;-ms-flex-align: start;-webkit-box-align: start;align-items: flex-start;-ms-flex-pack: justify;-webkit-box-pack: justify;justify-content: space-between; }
.gdk .txt-style-secondary { font-size: var(--text-3);font-weight: 400;line-height: 1.5em;--type-margin-top: var(--space-5);--type-margin-bottom: var(--space-5);-webkit-margin-before: var(--type-margin-top);margin-block-start: var(--type-margin-top);-webkit-margin-after: var(--type-margin-bottom);margin-block-end: var(--type-margin-bottom);color: var(--type-custom-color,var(--type-style-color));--type-style-color: var(--color-role-text-primary-base); }
.gdk .txt-style-secondary--m-0 { --type-margin-top: 0;--type-margin-bottom: 0; }
.gdk .site-footer-v2__country .site-footer-v2__country--selector { width: 100%; }
.gdk .site-footer-v2__country a { display: -ms-flexbox;display: -webkit-box;display: flex;-ms-flex-align: center;-webkit-box-align: center;align-items: center; }
.gdk .site-footer-v2__country svg { height: var(--icon-size-2);width: var(--icon-size-2);margin-left: var(--space-3);margin-right: var(--space-3); }
.gdk .site-footer-v2__country svg:first-child { height: var(--icon-size-3);width: var(--icon-size-3);-webkit-margin-start: 0;margin-inline-start: 0; }
.gdk .txt-style-utility { font-size: var(--text-2);font-weight: 400;line-height: 1.5em;--type-margin-top: var(--space-3);--type-margin-bottom: var(--space-3);-webkit-margin-before: var(--type-margin-top);margin-block-start: var(--type-margin-top);-webkit-margin-after: var(--type-margin-bottom);margin-block-end: var(--type-margin-bottom);color: var(--type-custom-color,var(--type-style-color));--type-style-color: var(--color-role-text-primary-base); }
.gdk .txt-style-utility--m-0 { --type-margin-top: 0;--type-margin-bottom: 0; }
.gdk .site-footer-v2__legal { font-size: var(--text-3);padding: 0;display: -ms-flexbox;display: -webkit-box;display: flex;list-style: none;-ms-flex-align: start;-webkit-box-align: start;align-items: flex-start; }
.gdk .site-footer-v2__country .site-footer-v2__legal { width: 100%; }
.gdk .site-footer-v2__legal li{ -webkit-margin-start: var(--space-5);margin-inline-start: var(--space-5);-webkit-margin-after: 0;margin-block-end: 0;display: -ms-flexbox;display: -webkit-box;display: flex;position: relative;-ms-flex-align: center;-webkit-box-align: center;align-items: center; padding-left: 0; }
.gdk .site-footer-v2__legal li::after { content: '';-webkit-margin-start: calc(-1*(var(--space-5)/2));margin-inline-start: calc(-1*(var(--space-5)/2));top: 50%;height: var(--space-4);position: absolute;-webkit-transform: translate(0,-50%);-ms-transform: translate(0,-50%);transform: translate(0,-50%);border-left: 1px solid var(--color-role-text-primary-base); }
.gdk .site-footer-v2__legal li:first-child { -webkit-margin-start: 0;margin-inline-start: 0; }
.gdk .theme--dark ul li:after { color: var(--color-role-text-primary-base); }
.gdk .footer-v2 ul li:before { display: none; }

@media (max-width: 1200px) {
   .gdk .site-footer-v2__country .site-footer-v2__legal { padding-left: 0.8em; -ms-flex-wrap:wrap;flex-wrap:wrap; }
}

@media (max-width: 767px) {
   .gdk .site-footer-v2__legal li { -webkit-margin-start: 0; margin-inline-start: 0; -webkit-margin-end: var(--space-5); margin-inline-end: var(--space-5); }
}

@media (min-width: 768px) {
    .gdk .site-footer-v2__country { -ms-flex-align: center;-webkit-box-align: center;align-items: center;-ms-flex-direction: row;-webkit-box-orient: horizontal;-webkit-box-direction: normal;flex-direction: row; }
    .gdk .site-footer-v2__country .site-footer-v2__country--selector { width: 50%; }
}

@media (min-width: 768px), (orientation: landscape) {
    .gdk .site-footer-v2__country, .gdk .site-footer-v2__legal { display: -ms-flexbox;display: -webkit-box;display: flex;-ms-flex-align: center;-webkit-box-align: center;align-items: center; }
    .gdk .site-footer-v2__legal { -ms-flex-pack: end;-webkit-box-pack: end;justify-content: flex-end; }
    .gdk .site-footer-v2__legal { display: -ms-flexbox;display: -webkit-box;display: flex;-ms-flex-align: center;-webkit-box-align: center;align-items: center; }
}

.sony-bar {
    top: 0;
    left: 0;
    font-size: 0;
    height: 36px;
    position: relative;
    width: 100%;
    z-index: 111;
}
.sony-bar {
    background-color: #000;
}
[dir=ltr] .sony-bar {
    text-align: right;
}
.sony-logo {
    display: inline-block;
    height: 100%;
    width: 100%;
}
.sony-bar__logo {
    width: 75px;
}
.sony-logo {
    background-image: url(../gfx/sony_logo-footer2023.svg);
    background-repeat: no-repeat;
    background-position: center;
}
.sony-bar__logo {
    margin: 0 8px;
}
@media screen and (max-width: 699px) {
    body > .sony-bar {
        display: none;
    }
}

@media screen and (min-width: 700px) {
    .footer-v2 > .sony-bar {
        display: none;
    }
}
.sie-logo { padding-bottom: 0.5em; }
@media (min-width: 992px) {
    .gdk .theme--dark ul li:nth-child(1)::after { display: none; }
}

/* text-a */
.text-a { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; color: var(--psin-black-200-static); font-weight: 400; }
.text-a.text-center { -webkit-box-align: center; -ms-flex-align: center; align-items: center; margin-left: auto; margin-right: auto; }
.text-a > * { margin-bottom: 2rem; }
.text-a > .btn-wrapper { margin-top: 1rem; }
.text-a > :first-child { margin-top: 0; }
.text-a > :last-child { margin-bottom: 0; }

@media (min-width: 992px) {
    .text-a > * { margin-bottom: 3rem; }
}

/* home-layout */
.home-layout { position: relative; z-index: 0; }

/* .home-header */
.home-header-wrapper { position: sticky; top: 0; left: 0; z-index: 12; width: 100%; min-width: 32rem; }
.home-header { background-color: #1f1f1f; height: 4.4rem; padding: 0.7rem 2rem; color: var(--psin-white-static); display: -webkit-box; display: -ms-flexbox; display: flex;-webkit-box-pack: justify;-ms-flex-pack: justify;justify-content: space-between; -webkit-box-align: center; -ms-flex-align: center; align-items: center; }
.home-header-logo { width: 3rem; color: var(--psin-white-static); position:relative; z-index:11; }

.ps-header-social { margin-left: auto; margin-right: -0.5rem; position:relative; z-index:11; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; }
.loginMenu > .dropdown-toggle > .btn { font-size: 1.4rem; padding: 0.5rem 1.4rem; }
.loginMenu > .dropdown-toggle > .btn > .btn-text { min-height: 2rem; }
.ps-share-icon { width: 2.4rem; -ms-flex-negative: 0; flex-shrink: 0; height: 2.4rem; font-size: 2.4rem; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; }
.ps-share-text { font-style: normal;font-weight: normal;font-size: 2.4rem;line-height: 1.33; text-decoration: none; color: var(--psin-white); }
.ps-navbar-share { padding: 0; margin: 0; -webkit-margin-start: 0.4rem; margin-inline-start: 0.4rem; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; }
.ps-navbar-share li { list-style: none; padding: 0; margin: 0; display: block; }
.ps-navbar-share li::before { display: none; }
.ps-share-link { padding: 0.5rem; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; font-style: normal;font-weight: normal;font-size: 2.4rem;line-height: 1.33; text-decoration: none; color: var(--psin-white) !important; border: 0 !important; background-color: transparent !important; }
.ps-share-link:focus-visible { border-radius: 50%; }

.shareMenu .dropdown-toggle::after { display: none; }
.shareMenu ul.dropdown-menu > li::before { display: none; }
.dropdown-menu.share-dd-menu { min-width: 22rem; width: 100%; border: none; background: none; }
.share-dd-box { padding: 2.5rem 1.6rem 2.4rem 1.6rem; background: #f5f5f5;border: 1px solid var(--psin-white);border-radius: 1.2rem; position: relative; z-index: 1; }
.shareMenu .dropdown-menu { top: 4.8rem !important; border-radius: 0;padding: 0;border: 0;background-color: #f5f5f5;color: #1F1F1F;position: absolute; width: auto; min-width: 100%; }
.shareMenu .dropdown-item { display: -webkit-box; display: -ms-flexbox; display: flex;font-size: 1.4rem;line-height: 1.8rem;padding: 1.6rem 2.2rem;font-weight: 400;background-color: #f5f5f5;color: #1F1F1F; }
.shareMenu ul.share-dd-menu > li::before { display: none; }
.shareMenu .dropdown-item i { margin: 0 1.6rem 0 0; font-size: 1.5em; }
.shareMenu .dropdown-item:focus,
.shareMenu .dropdown-item:hover,
.shareMenu .dropdown-item.active,
.shareMenu .dropdown-item:active { color: #1F1F1F;background-color: var(--psin-white); }
.share-dd-menu::before { display: block; }

.ps-navbar-login { padding: 0; margin: 0; -webkit-margin-start: 8px; margin-inline-start: 8px; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; }
.ps-navbar-login li { list-style: none; padding: 0; margin: 0; display: block; }
.ps-navbar-login li::before { display: none; }

.loginMenu .dropdown-toggle::after { display: none; }
.loginMenu ul.dropdown-menu > li::before { display: none; }
.loginMenu > .dropdown-toggle { padding: 0.5rem 1.4rem; font-size: 1.4rem; text-decoration: none !important; }
.loginMenu > .dropdown-toggle > i { font-size: 1.2rem; }
.loginMenu > .dropdown-toggle > .btn-text {min-height: 2rem; }

.dropdown-menu.login-dd-menu { min-width: 24.6rem; width: 100%; top: 4.7rem !important; right: -3rem; padding: 1.4rem 1rem;border: none; background: var(--psin-white); -webkit-box-shadow: 0px 0.5rem 1.5rem 0px rgba(0, 0, 0, 0.20); box-shadow: 0px 0.5rem 1.5rem 0px rgba(0, 0, 0, 0.20); border-radius: 1.5rem; -webkit-border-radius: 1.5rem; -moz-border-radius: 1.5rem; -ms-border-radius: 1.5rem; -o-border-radius: 1.5rem;
}
.dropdown-menu.login-dd-menu > li::before { display: none; }
.login-dd-menu > li { margin-bottom: 1.4rem; }
.login-dd-menu > li:last-child { margin-bottom: 0; }
.login-dd-menu > li > .btn { margin-top: 0; -webkit-box-pack: start; -ms-flex-pack: start; justify-content: flex-start; }
.login-dd-menu > li > .btn > i { font-size: 1.7rem; }
.login-dd-menu > li > .btn > .btn-text { -webkit-box-flex: 1; -ms-flex: 1; flex: 1; width: 1%; text-align: center; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; }

.linkedMenu { padding: 0; margin: 0; list-style: none; display: block; }
.linkedMenu::before { display: none; }
.linkedMenu .dropdown-toggle::after { display: none; }
.linkedMenu ul.dropdown-menu > li::before { display: none; }
.linkedMenu > .ps-tp-row.dropdown-toggle .ps-profile-down-arrow i { transform: rotate(0deg); -webkit-transform: rotate(0deg); -moz-transform: rotate(0deg); -ms-transform: rotate(0deg); -o-transform: rotate(0deg); }
.linkedMenu > .ps-tp-row.dropdown-toggle.show .ps-profile-down-arrow i { transform: rotate(180deg); -webkit-transform: rotate(180deg); -moz-transform: rotate(180deg); -ms-transform: rotate(180deg); -o-transform: rotate(180deg); }

.dropdown-menu.linked-dd-menu { min-width: 24.6rem; width: 100%; top: 4.7rem !important; right: -3rem; padding: 1.4rem 1rem;border: none; background: var(--psin-white); -webkit-box-shadow: 0px 0.5rem 1.5rem 0px rgba(0, 0, 0, 0.20); box-shadow: 0px 0.5rem 1.5rem 0px rgba(0, 0, 0, 0.20); border-radius: 1.5rem; -webkit-border-radius: 1.5rem; -moz-border-radius: 1.5rem; -ms-border-radius: 1.5rem; -o-border-radius: 1.5rem; }
.dropdown-menu.linked-dd-menu > li::before { display: none; }
.linked-dd-menu > li { margin-bottom: 1.4rem; padding: 0; list-style: none; }
.linked-dd-menu > li:last-child { margin-bottom: 0; }
.linked-dd-menu > li > .btn { margin-top: 0; -webkit-box-pack: start; -ms-flex-pack: start; justify-content: flex-start; }
.linked-dd-menu > li > .btn > i { margin-right: 0.8rem; font-size: 1.7rem; }
.linked-dd-menu > li > .btn > .btn-text { display: -webkit-box; display: -ms-flexbox; display: flex; }
.check-linked-icon { margin-left: auto; -ms-flex-negative: 0; flex-shrink: 0; width: 1.7rem; height: 1.7rem; color: #00A088; background-color: #fff; border-radius: 50%; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; -webkit-border-radius: 50%; -moz-border-radius: 50%; -ms-border-radius: 50%; -o-border-radius: 50%; }
.check-linked-icon i { font-size: 1.7rem; }
.check-linked-icon.green-bg { color: #fff; background-color: #00A088; }
.check-linked-icon .cc_icon-close-2 { color: #FA3939; }

@media (min-width: 768px) {
    .ps-share-link { color: var(--psin-black-200) !important; }
    .home-header { height: 6.4rem; padding: 0.7rem 2.4rem;  background-color: var(--psin-white); }
    .home-header-logo { width: 4rem; }
    .loginMenu > .dropdown-toggle { padding: 0.7rem 1.4rem; }

    .loginMenu .btn-white { background-color: #0070CC !important; color: #fff !important; border-color: transparent; }
    .loginMenu .btn-white:hover { background-color: #0064b7 !important; color: #fff !important; border-color: transparent; }
    .loginMenu .btn-white:focus { background-color: #0059a3 !important; color: #fff !important; border-color: transparent; }
    .loginMenu .btn-white:active { background-color: #0059a3 !important; color: #fff !important; border-color: transparent !important; }

    .loginMenu .btn-white::before,
    .loginMenu .btn-white:hover::before { -webkit-box-shadow: 0 0 0 0.2rem #0064b7; box-shadow: 0 0 0 0.2rem #0064b7; }
    .loginMenu .btn-white:focus::before { -webkit-box-shadow: 0 0 0 0.2rem #0059a3; box-shadow: 0 0 0 0.2rem #0059a3; }
    .loginMenu .btn-white:active::before { -webkit-box-shadow: 0 0 0 0.2rem #0059a3; box-shadow: 0 0 0 0.2rem #0059a3; }
}

@media (min-width: 992px) {
    .dropdown-menu.login-dd-menu { min-width: 40.8rem; top: 7.4rem !important; right: -3.4rem; padding: 3.2rem; border-radius: 3rem; -webkit-border-radius: 3rem; -moz-border-radius: 3rem; -ms-border-radius: 3rem; -o-border-radius: 3rem; }
    .dropdown-menu.linked-dd-menu { min-width: 40.8rem; top: 7.4rem !important; right: -3.4rem; padding: 1.8rem 3.2rem 3.2rem 3.2rem; border-radius: 3rem; -webkit-border-radius: 3rem; -moz-border-radius: 3rem; -ms-border-radius: 3rem; -o-border-radius: 3rem; }
    .login-dd-menu > li { margin-bottom: 1.8rem; }
    .login-dd-menu > li > .btn > i { font-size: 2.8rem; }
    .linked-dd-menu > li { margin-bottom: 1.8rem; }
    .linked-dd-menu > li > .btn > i { margin-right: 1.8rem; font-size: 2.8rem; }
    .check-linked-icon { width: 2.8rem; height: 2.8rem; }
    .check-linked-icon i { font-size: 2.8rem; }
}

.ps-tp-space { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; }
.ps-tp-cell { padding: 0 0.7rem; }
.ps-tp-row { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; text-decoration: none !important; }
.ps-tp-icon { font-size: 2.8rem; -ms-flex-negative: 0; flex-shrink: 0; color: var(--psin-white); }
.ps-tp-text { margin-left: 0.7rem; font-size: var(--cc-font-size); line-height: 1.5; font-style: normal; font-weight: 700; color: var(--psin-white); display: block; }
.ps-profile-avtar { width: 2.6rem; -ms-flex-negative: 0; flex-shrink: 0; margin: 0.1rem; }
.ps-profile-down-arrow { width: 1.4rem; font-size: 1.4rem; -ms-flex-negative: 0; flex-shrink: 0; margin-left: 0.7rem; color: #fff; }

@media (min-width: 768px) {
    .ps-tp-cell { padding: 0 1rem; }
    .ps-tp-icon { font-size: 3.6rem; color: var(--psin-black-200); }
    .ps-tp-text { color: var(--psin-black-200); }
    .ps-profile-avtar { width: 3.2rem; margin: 0.2rem; }
    .ps-profile-down-arrow { color: #1f1f1f; }
}

@media (min-width: 1400px) {
    .ps-tp-space { padding-right: 0.8rem; }
    .ps-tp-cell { padding: 0 1.8rem; }
    .ps-tp-icon { font-size: 5rem; }
    .ps-profile-avtar { width: 4.2rem; -ms-flex-negative: 0; flex-shrink: 0; margin: 0.4rem; }
}

.cc-video-poster { position: absolute; top: 0; right: 0; bottom: 0; left: 0;display: -webkit-box;display: -ms-flexbox;display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; border: none; pointer-events: none; }
.cc_imgbox .cc-video { border-radius: 0; }
iframe.cc-video { height: 300%; top: -100%; }

/* swiper start */
.swiper-pagination { --swiper-pagination-bottom: auto; margin-top: 0.2rem; }
.swiper-pagination-bullet { --swiper-pagination-bullet-width: 1.5rem; --swiper-pagination-bullet-height: 0.3rem; --swiper-pagination-bullet-border-radius: 0; --swiper-pagination-bullet-horizontal-gap: 0.4rem !important; --swiper-pagination-bullet-inactive-opacity: 1; background-color: #F5F7FA; }
.swiper-pagination-bullet.swiper-pagination-bullet-active { background-color: #0070CC; }

.cc-slider-btns { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; position: absolute; top: 50%; -webkit-transform: translateY(-50%); -ms-transform: translateY(-50%); transform: translateY(-50%); left: -1.5rem; right: -1.5rem; -webkit-box-pack: justify; -ms-flex-pack: justify; justify-content: space-between; z-index: 5; pointer-events: none; }
.cc-slider-btn { display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; margin: 0 0.5rem; background-color: #0070CC; color: #fff !important; width: 4.4rem; height: 4.4rem; border-radius: 50%; -ms-flex-negative: 0; flex-shrink: 0; font-size: 2.4rem; pointer-events: auto; }
.cc-slider-btn.swiper-button-disabled { opacity: 0; visibility: hidden; }

@media (min-width: 768px) {
    .swiper-pagination-bullet { --swiper-pagination-bullet-width: 2.6rem; }
    .cc-slider-btns { left: -4rem; right: -4rem; }
}

/* swiper end */

/* s1 */
.cc-s1 { background: -o-linear-gradient(left,rgba(114, 106, 253, 1) 0%, rgba(9, 175, 251, 1) 100%); background: -webkit-gradient(linear,left top, right top,from(rgba(114, 106, 253, 1)), to(rgba(9, 175, 251, 1))); background: linear-gradient(90deg,rgba(114, 106, 253, 1) 0%, rgba(9, 175, 251, 1) 100%); z-index: 11; }
.cc-s1 .cc-section-bg picture,
.cc-section-bg iframe,
.cc-section-bg video,
.cc-section-bg img { -o-object-position: center top; object-position: center top; }

.cc-s1-left .text-a { font-weight: 400; text-align: center; -webkit-box-align: center; -ms-flex-align: center; align-items: center; }
.cc-s1-left .text-a .h1 { margin-bottom: 1.5rem; color: #fff; font-weight: 700; }
.cc-s1-left .btn { min-width: 12.1rem; }
.cc-s1-l-thumb { width: 100%; max-width: 24rem; margin-left: auto; margin-right: auto; margin-bottom: 0; --bs-aspect-ratio: 48.73188405797101%; }
.dd-wrap-a > .btn > i { font-size: 1.6rem; }
.dd-wrap-a .dropdown-menu.login-dd-menu > li { padding-left: 0; }

@media (max-width: 767px) {
    .cc-s1 { padding-top: 73.06666666666667%; }
    .cc-s1-left .text-a .h1 { margin-top: 1.4rem; }
}

@media (min-width: 768px) {
    .cc-s1-l-thumb { max-width: 33rem; }
}

@media (min-width: 992px) {
    .cc-s1-left .btn { min-width: 16.9rem; }
    .cc-s1-l-thumb { max-width: 39rem; }
    .dd-wrap-a .dropdown-menu.login-dd-menu { right: -8.4rem; }
}

@media (min-width: 1400px) {
    .cc-s1 { padding: 13rem 0 12rem 0; }
    .cc-s1-l-thumb { max-width: 55.2rem; }
}
/* s1 */

/* s6 */
.cc-s6 { background-color: #fff; z-index: 10; }
.cc-s6-slider-part { position: relative; }
.cc-s6-slider { overflow: visible; }
.cc-s6-slider .swiper-slide { width: 100%; max-width: 100%; -webkit-box-flex: 0; -ms-flex: 0 0 100%; flex: 0 0 100%; }
.cc-pd-tagname { margin-bottom: 1.4rem; display: -webkit-box; display: -ms-flexbox; display: flex; }
.cc-pd-tagname-box { padding: 0.5rem 2rem 0.7rem 2rem; display: -webkit-box; display: -ms-flexbox; display: flex; position: relative; background-color: #0070CC; z-index: 1; transform: skewX(-15deg); -webkit-transform: skewX(-15deg); -moz-transform: skewX(-15deg); -ms-transform: skewX(-15deg); -o-transform: skewX(-15deg); }
.cc-pd-tagname-box-in { padding: 0; font-size: 1.7rem; line-height: 1.5; font-style: normal; font-weight: 300; color: #FFF; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align:center; -ms-flex-align:center; align-items:center; position: relative; z-index: 11; transform: skewX(15deg); -webkit-transform: skewX(15deg); -moz-transform: skewX(15deg); -ms-transform: skewX(15deg); -o-transform: skewX(15deg); }
.cc-pd-tagname-box-in > i { margin-right: 0.4rem; font-size: 2rem; -ms-flex-negative: 0; flex-shrink: 0; color: #FABE00; display: -webkit-box; display: -ms-flexbox; display: flex; }
.cc-pd-tagname-text { top: 0.115em; position: relative; }
.cc-s6-t .h2 { color: #171D2E; }

.cc-s6-slide-box-thumb { margin-bottom: 0; }
.cc-s6-slider .h3 { color: #0070CC; }
.cc-s6-slider .text-a .h3 { margin-top: 0; }
.dd-wrap-b { position: relative; }
.dd-wrap-b > .btn > i { font-size: 1.6rem; }
.dd-wrap-b .dropdown-menu.login-dd-menu { right: -5rem; }
.dd-wrap-b .dropdown-menu.login-dd-menu > li { padding-left: 0; }

@media (min-width: 768px) {
    .cc-s6-slider { margin: 0 -1.5rem; }
    .cc-s6-slider .cc-s6-slide-box-thumb { max-width: 50rem; }
}

@media (min-width: 992px) {
    .cc-s6-slider .cc-slider-btns { display: none; }
    .cc-pd-tagname { margin-bottom: 3.6rem; }
    .cc-s6-slider .swiper-wrapper { -webkit-box-align: center; -ms-flex-align: center; align-items: center; }
    .dd-wrap-b .dropdown-menu.login-dd-menu { right: -11.4rem; }
}

@media (min-width: 1200px) {
    .cc-pd-tagname-box { padding: 0.8rem 2.4rem 1.3rem 2.4rem; }
    .cc-pd-tagname-box-in { font-size: 2.4rem; }
}

@media (min-width: 1400px) {
    .cc-s6-slider { margin: 0 -5.7rem; }
    .cc-s6-slider-part .cc-pd-tagname { margin-bottom: 3.6rem; }
}

@media (min-width: 1920px) {
    .cc-s6 .container { max-width: 1734px; }
    .cc-pd-tagname-box-in { font-size: 3.5rem; }
    .cc-s6-slider .cc-s6-slide-box-thumb { max-width: 84rem; }
}
/* s6 */


/* s2 */
.cc-s2 { background: -o-linear-gradient(top,rgba(114, 106, 253, 1) 0%, rgba(9, 175, 251, 1) 100%); background: -webkit-gradient(linear,left top, left bottom,from(rgba(114, 106, 253, 1)), to(rgba(9, 175, 251, 1))); background: linear-gradient(180deg,rgba(114, 106, 253, 1) 0%, rgba(9, 175, 251, 1) 100%); z-index: 9; }
.cc-s2 .cc-section-bg picture,
.cc-s2 .cc-section-bg iframe,
.cc-s2 .cc-section-bg video,
.cc-s2 .cc-section-bg img { -o-object-position: top center; object-position: top center; }
.cc-s2-mid { width: 100%; margin: 1.4rem auto; border-radius: 1.8rem; overflow: hidden; border: 0.8rem solid rgba(255, 255, 255, 0.15); -webkit-border-radius: 1.8rem; -moz-border-radius: 1.8rem; -ms-border-radius: 1.8rem; -o-border-radius: 1.8rem; -webkit-box-shadow: 0px 0.5rem 1.5rem 0px rgba(0, 0, 0, 0.20); box-shadow: 0px 0.5rem 1.5rem 0px rgba(0, 0, 0, 0.20); }
.cc-s2-mid .cc-video-poster { pointer-events: none; z-index: 10; }
.cc-s2-mid .cc-video { overflow: hidden; border-radius: 1.5rem; -webkit-border-radius: 1.5rem; -moz-border-radius: 1.5rem; -ms-border-radius: 1.5rem; -o-border-radius: 1.5rem; }
.cc-s2-mid .cc-video-poster iframe,
.cc-s2-mid .cc-video-poster img,
.cc-s2-mid .cc-video-poster video { border-radius: 0.8rem; overflow: hidden; -webkit-border-radius: 0.8rem; -moz-border-radius: 0.8rem; -ms-border-radius: 0.8rem; -o-border-radius: 0.8rem; }
.cc-s2-mid .cc_icon-play-with-outline { color: #fff; -webkit-filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.35)); filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.35)); z-index: 1; font-size: 5rem; }
.cc-s2-mid .cc-video:focus + .cc-video-poster { outline-offset: 2px !important; outline: 2px solid #1F1F1F !important; }

.cc-s2-glry { margin-top: 3.2rem; }
.cc-s2-glry  .text-a { -webkit-box-orient: horizontal; -webkit-box-direction: normal; -ms-flex-direction: row; flex-direction: row; -webkit-box-align: center; -ms-flex-align: center; align-items: center; margin-bottom: 1.4rem; }
.cc-s2-t { max-width: 55.2rem; margin: 0 auto; }
.cc-s2-t .text-a .h2 { margin-bottom: 1.4rem; }
.cc-s2-thumb { width: 100%; max-width: 4.5rem; margin: 0 1.4rem 0 0; -ms-flex-negative: 0; flex-shrink: 0 }
.cc-s2-glry p { width: 1%; -webkit-box-flex: 1; -ms-flex: 1 1 auto; flex: 1 1 auto; }

.cc-s2-t-btn { max-width: 40.8rem; margin-left: auto; margin-right: auto; display: flex; justify-content: center; }

@media (min-width: 768px) {
    .cc-s2 { background: -o-linear-gradient(left,rgba(114, 106, 253, 1) 0%, rgba(9, 175, 251, 1) 100%); background: -webkit-gradient(linear,left top, right top,from(rgba(114, 106, 253, 1)), to(rgba(9, 175, 251, 1))); background: linear-gradient(90deg,rgba(114, 106, 253, 1) 0%, rgba(9, 175, 251, 1) 100%); }
    .cc-s2-glry  .text-a { -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; margin-bottom: 0; }
    .cc-s2-glry p { width: auto; }
    .cc-s2-thumb { max-width: 6.9rem; margin: 0 auto 0.5rem auto; }
    .cc-s2-t .text-a .h2 { margin-bottom: 1.8rem; }
    .cc-s2-t-btn { margin-top: 5rem; }
    .cc-s2-mid { margin-top: 3.6rem; border-radius: 3.5rem; border: 1.5rem solid rgba(255, 255, 255, 0.15); -webkit-box-shadow: 0px 0.5rem 1.5rem 0px rgba(0, 0, 0, 0.20); box-shadow: 0px 0.5rem 1.5rem 0px rgba(0, 0, 0, 0.20); -webkit-border-radius: 3.5rem; -moz-border-radius: 3.5rem; -ms-border-radius: 3.5rem; -o-border-radius: 3.5rem; }
    .cc-s2-mid .cc_icon-play-with-outline { font-size: 7rem; }
    .cc-s2-glry { margin-top: 3.6rem; }
}

@media (min-width: 768px) and (max-width: 1024px) {
    .cc-s2-glry p { font-size: 1.4rem; }
}

@media (min-width: 1400px) {
    .cc-s2-glry .row>* { max-width: 27rem; }
    .cc-s2-mid .cc_icon-play-with-outline { font-size: 8rem; }
}
/* s2 */

/* s3 */
.cc-s3 { background-color: #fff; }
.cc-s3-t .text-a .h2 { margin-bottom: 1.4rem; }
.cc-s3-slider { margin: 1.4rem auto 3.1rem auto; max-width: 128.9rem; }
.js-credit-slider:not(.swiper-initialized) .swiper-slide { margin: 0 1.5rem; }
.cc-s3-slider .swiper { overflow: visible; }
.cc-s3-slider .swiper-slide { height: auto; width: 25.8rem !important; border-radius: 1.5rem; background-color: #FFF; -webkit-box-shadow: 0px 0.5rem 1.5rem 0px rgba(0, 0, 0, 0.10); box-shadow: 0px 0.5rem 1.5rem 0px rgba(0, 0, 0, 0.10); -webkit-border-radius: 1.5rem; -moz-border-radius: 1.5rem; -ms-border-radius: 1.5rem; -o-border-radius: 1.5rem; }
.cc-s3-box { position: relative; border-radius: 1.5rem; padding: 0.8rem; height: 100%; -webkit-box-shadow: 0 0.1rem 1rem rgba(0, 0, 0, 0.1); box-shadow: 0 0.1rem 1rem rgba(0, 0, 0, 0.1); -webkit-transition: -webkit-box-shadow .2s ease-in-out; transition: -webkit-box-shadow .2s ease-in-out; transition: box-shadow .2s ease-in-out; transition: box-shadow .2s ease-in-out, -webkit-box-shadow .2s ease-in-out; background-color: #fff; -webkit-border-radius: 1.5rem; -moz-border-radius: 1.5rem; -ms-border-radius: 1.5rem; -o-border-radius: 1.5rem; -webkit-transition: box-shadow .2s ease-in-out; -moz-transition: box-shadow .2s ease-in-out; -ms-transition: box-shadow .2s ease-in-out; -o-transition: box-shadow .2s ease-in-out; }
.cc-s3-box:hover { -webkit-box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.1); box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.1); }
.cc-s3-box .h3 { margin: 3.4rem 0 2rem 0; }

.js-credit-slider .swiper-pagination { margin-top: 1.5rem; }

.cc-s3-box-gfx { margin-bottom: 0.7rem; overflow: hidden; border-radius: 1.5rem; background-color: #fff; -webkit-box-shadow: 0px 0.5rem 1.5rem 0px rgba(0, 0, 0, 0.20); box-shadow: 0px 0.5rem 1.5rem 0px rgba(0, 0, 0, 0.20); -webkit-border-radius: 1.5rem; -moz-border-radius: 1.5rem; -ms-border-radius: 1.5rem; -o-border-radius: 1.5rem; }
.cc-s3-box-desc { padding: 0.8rem 0.8rem 0 0.8rem ; }
.cc-s3-box-desc .h5 { margin-bottom: 1rem; }
.cc-s3-box-prize { margin-bottom: 1rem; color: #000;font-size: 2rem;font-style: normal;font-weight: 400;line-height: 1.25;}

@media (max-width: 767px) {
    .cc-s3-box-desc .h5 { font-size: 2rem; }
}

@media (min-width: 768px) {
    .cc-s3-slider { margin: 3.6rem auto; }
    .cc-s3-slider .swiper-slide { width: 27.8rem !important; }
    .cc-s3-t .text-a .h2 { margin-bottom: 1.8rem; }
    .cc-s3-box { margin-bottom: 1.5rem; border-radius: 3rem; -webkit-border-radius: 3rem; -moz-border-radius: 3rem; -ms-border-radius: 3rem; -o-border-radius: 3rem; }
    .cc-s3-box-desc .h5 { margin-bottom: 1.6rem; }
    .cc-s3-box-prize { margin-bottom: 2.7rem; font-size: 2.8rem; }
    .js-credit-slider .swiper-pagination { margin-top: 1.8rem; }
}

@media (min-width: 992px) {
    .cc-s3-slider .swiper-slide { border-radius: 3rem; -webkit-border-radius: 3rem; -moz-border-radius: 3rem; -ms-border-radius: 3rem; -o-border-radius: 3rem; }
    .cc-s3-box { padding: 1.5rem; border-radius: 3rem; -webkit-border-radius: 3rem; -moz-border-radius: 3rem; -ms-border-radius: 3rem; -o-border-radius: 3rem; }
    .cc-s3-box-gfx { border-radius: 3rem; -webkit-border-radius: 3rem; -moz-border-radius: 3rem; -ms-border-radius: 3rem; -o-border-radius: 3rem; }
    .cc-s3-box-desc { padding: 1.5rem 1.5rem 0 1.5rem; }
}

@media (min-width: 1200px) {
    .cc-s3-slider .swiper-slide { width: 30.8rem !important; }
}

@media (min-width: 1400px) {
    .cc-s3-slider .swiper-slide { width: 34.8rem !important; }
}

@media (min-width: 1920px) {
    .cc-s3-slider .swiper-slide { width: 40.8rem !important; }
}
/* s3 */

/* s4 */
.cc-s4 { background-color: #F5F7FA; }
.cc-s4-glry { display: -webkit-box; display: -ms-flexbox; display: flex; -ms-flex-wrap: wrap; flex-wrap: wrap; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; margin: 1.4rem -0.7rem 0 -0.7rem; }
.cc-s4-glry-box { font-size: 5rem; -ms-flex-negative: 0; flex-shrink: 0; padding: 0; margin: 0 0.7rem; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; color: #000; }

@media (max-width: 767px) {
    .cc-s4-head .text-a .h2 { font-size: 2.4rem; }
}

@media (min-width: 375px) {
    .cc-s4-glry-box { font-size: 7.2rem; }
}

@media (min-width: 768px) {
    .cc-s4-glry { margin: 3.6rem -2rem 0 -2rem; }
    .cc-s4-glry-box { font-size: 8rem; margin: 0 2rem; }
}

@media (min-width: 1600px) {
    .cc-s4-glry { margin: 3.6rem -3.6rem 0 -3.6rem; }
    .cc-s4-glry-box { font-size: 12rem; margin: 0 3.6rem; }
}
/* s4 */

/* s5 */
.cc-s5 { background-color: #fff; }
.cc-s5-heading { margin-bottom: 2.4rem; }

.cc-accordion-wrapper { margin: 0; }
.cc-accordion { margin: 0; }
.cc-accordion:last-child { margin-bottom: 0; }
.cc-accordion-handle { width: 100%; min-height: 4.9rem; text-align: left; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; position: relative; border: none; background: none; color: #000; font-weight: 400; font-size: 1.4rem; line-height: 1.5; margin: 0; padding: 1.4rem 3.2rem 1.4rem 1.4rem; transition: all 0.15s ease; -webkit-transition: all 0.15s ease; -moz-transition: all 0.15s ease; -ms-transition: all 0.15s ease; -o-transition: all 0.15s ease; }
.cc-accordion-handle i.cc_icon-minus-twex,
.cc-accordion-handle i.cc_icon-plus-twex { position: absolute; top: 1.7rem; right: 1.4rem; font-size: 1.4rem; color: #000; -webkit-transition: all 0.15s ease; -o-transition: all 0.15s ease; transition: all 0.15s ease; }
.cc-accordion-handle i.cc_icon-minus-twex { opacity: 0; -webkit-transition: opacity 0.150s; -o-transition: opacity 0.150s; transition: opacity 0.150s; }
.cc-accordion-handle[aria-expanded=true] i.cc_icon-minus-twex { opacity: 1; }
.cc-accordion-handle[aria-expanded=true] i.cc_icon-plus-twex { opacity: 0; }
.cc-accordion-handle:hover { color: #fff; background-color: #0070CC; }
.cc-accordion-handle:hover i.cc_icon-minus-twex,
.cc-accordion-handle:hover i.cc_icon-plus-twex { color: #fff; }
.cc-accordion-content { padding: 0.2rem 1.4rem 1.4rem 1.4rem; margin: 0; position: relative; }
.cc-accordion-body { display: none; position: relative; }
.cc-accordion-body.show { display: block; }
.cc-accordion-wrapper .cc-accordion:nth-child(odd) { background-color: #F5F7FA;  }

@media (min-width: 768px) {
    .cc-s5-heading { margin-bottom: 3rem; }
    .cc-accordion-wrapper { margin: 0; }
    .cc-accordion-handle { min-height: 7.9rem; font-weight: 500; font-size: 1.8rem; padding: 1.2em 5rem 1.1em 2.4rem; }
    .cc-accordion-handle i.cc_icon-minus-twex,
    .cc-accordion-handle i.cc_icon-plus-twex { top: 2.8rem; right: 2.4rem; font-size: 2.4rem; }
    .cc-accordion-content { padding: 0.2rem 5rem 3.6rem 2.4rem; }
}

@media (min-width: 1400px) {
    .cc-accordion-handle { min-height: 9.9rem; padding: 1.2em 6rem 1.1em 3.6rem; }
    .cc-accordion-handle i.cc_icon-minus-twex,
    .cc-accordion-handle i.cc_icon-plus-twex { top: 3.6rem; right: 3.6rem; }
    .cc-accordion-content { padding: 0.2rem 13rem 3.6rem 3.6rem; }
}

/* count down */
.cc-countdown-page { background-color: #0143f9; position: relative; z-index: 1; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; min-height: var(--cc-fs-height); }
.cc-countdown-page-in { max-height: 100%; overflow: hidden; padding: 4rem 2rem; position: relative; z-index:11; -webkit-box-flex: 1; -ms-flex: 1  1 auto; flex: 1  1 auto; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; }
.cc-countdown-page-in::-webkit-scrollbar { width: 0.5rem; }
.cc-countdown-page-in::-webkit-scrollbar-track { background: #dadada; border-radius: 0.6rem; }
.cc-countdown-page-in::-webkit-scrollbar-thumb { background: #848484; border-radius: 0.6rem; }
.cc-countdown-page-in::-webkit-scrollbar-thumb:hover { background-color: #848484; }
.cc-countdown-page-content { padding-top: 38.26865671641791%; width: 100%; max-width: 33.5rem; margin: 0 auto; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; -webkit-box-flex: 1; -ms-flex: 1 1 auto; flex: 1 1 auto; -webkit-box-align: start; -ms-flex-align: start; align-items: flex-start; }
.cc-cp-head-wrapper { width: 100%; }
.cc-cp-ps-logo { width: 100%; max-width: 20rem; margin: 0 auto 2rem auto; display: block; position: relative; outline: 0 !important; }
.cc-cp-ps-logo::before { padding-top: 48.63309352517986%; display: block; content: ''; }
.cc-cp-ps-logo img { display: block; width: 100%; height: 100%; top: 0; left: 0; position: absolute; z-index: 1; -o-object-fit: contain; object-fit: contain;  }

.cc-countdown-wrap { width: 100%; }
.cc-countdown-group { width: 100%; }
.cc-countdown-row { margin: 0 -0.7rem; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; }
.cc-countdown-cell { position: relative; padding: 0 0.7rem; width: 25%; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; -webkit-box-orient: vertical; -webkit-box-direction: normal; -ms-flex-direction: column; flex-direction: column; }
.cc-countdown-text-cell { width: 100%; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; padding: 1em 0.733em; background-color: #fff; overflow: hidden; }
.cc-countdown-cell strong { display: block; font-size: 2.733em; line-height: 1; font-weight: 800; color: #0070CC; text-align: center; min-width: 50%; font-family: 'Bebas Neue'; }
.cc-countdown-cell span { margin-top: 0.6rem; font-size: 1.2rem; line-height: 1.2; color: #fff; text-transform: uppercase; font-weight: 800;font-family: 'Bebas Neue';display: block; }

.cc-countdown-code { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: 2; background-color: #fff; }
.cc-countdown-code-in { width: 100%; height: 100%; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-align: center; -ms-flex-align: center; align-items: center; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; }
.cc-countdown-code-text  { display: block; font-size: 3rem; line-height: 1.304; font-weight: 700; color: #1f1f1f; }

.cc-countdown-header { color: #fff; margin-bottom: 0rem; }
#countdown-in { color: #fff; }


/* html.stream-countdown {  }
html.stream-countdown .cc-countdown-group { padding-top: 2rem; max-width: 52.2rem; }
html.stream-countdown .cc-countdown-cell { min-width: 17.9rem; } */

html.stream-countdown .cc-countdown-group { padding-top: 3rem; }

@media (max-width: 359px) {
    .cc-countdown-header .h1 { font-size: 2.4rem; }
}

@media (min-width: 360px) {
    .cc-countdown-cell strong { font-size: 3.733em; }
}

@media (min-width: 375px) {
    .cc-countdown-page-content { padding-top: 46.26865671641791%; }
    .cc-cp-ps-logo { max-width: 24rem; }
    .cc-countdown-cell span { font-size: 1.6rem; }
    .cc-countdown-header .h2 { font-size: 3.5rem; }
}

@media (min-width: 576px) {
    .cc-countdown-page-in { padding: 4rem; }
}

@media (min-width: 768px) {
    .cc-countdown-page-content { max-width: 45.6rem; padding-top: 0; margin: 0; }
    .cc-cp-ps-logo { max-width: 100%; margin: 0 auto 4rem auto; }
    .cc-countdown-row { margin: 0 -1rem; }
    .cc-countdown-cell { padding: 0 1rem; }
    .cc-countdown-cell strong { font-size: 5.5em; }
    .cc-countdown-code-text { font-size: 8vw; line-height: 1; }
    .cc-countdown-header { margin-bottom: 0.6rem; }
}

@media (min-width: 1200px) {
    .cc-countdown-page-content { max-width: 56.2rem; }
    .cc-cp-ps-logo { max-width: 68%; margin: 0 0 4rem 0; }
    .cc-countdown-text-cell { padding: 0.667em 1.111em; }
    .cc-countdown-cell strong { font-size: 7em; }
}

@media (min-width: 1400px) {
    .cc-countdown-page-in { padding: 6rem; }
    .cc-countdown-header { margin-bottom: 1.2rem; }
}

@media (min-width: 1920px) {
    .cc-countdown-page-content { max-width: 69.6rem; }
    .cc-countdown-page-in { padding: 7.5rem 9.8rem; }
    .cc-cp-ps-logo { max-width: 75%; margin: 0 0 5rem 0; }
    .cc-countdown-cell span { margin-top: 1.5rem; font-size: 2.1rem; }
    .cc-countdown-cell strong { font-size: 7.5em; }
}

@media (min-width: 1920px) and (min-height: 1080px) {
    .cc-countdown-page-in { padding: 8.8rem 10.8rem; }
    .cc-cp-ps-logo { margin: 0 0 6rem 0; }
}

@media (min-width: 2560px) {
    .cc-countdown-page-in { padding: 10rem 6rem; }
    .cc-cp-ps-logo { max-width: 80%; margin: 0 0 5rem 0; }
}

@media (min-width: 2560px) and (min-height: 1420px) {
    .cc-countdown-page-in { padding: 12rem 6rem; }
}

.cc-countdown-form-wrap { width: 100%; }
.cc-countdown-form { margin-top: 2rem; max-width: 40.8rem; width: 100%; }
.form-group { margin-bottom: 1.4rem; }
.form-label { margin-bottom: 0.3rem; color: #FFF;font-size: 1.4rem;font-style: normal;font-weight: 700;line-height: 1.4; display: block; }
.form-control-wrapper { position: relative; }
.form-control { display: block; width: 100%; height: 4.2rem; padding: 1rem 1.4rem; font-size: 1.2rem; font-weight: 400; line-height: 1.5; color: #000; -webkit-appearance: none; -moz-appearance: none; appearance: none; background-color: #fff; background-clip: padding-box; border: 0.1rem solid #F5F7FA; border-radius: 0.5rem; -webkit-transition: border-color .15s ease-in-out, -webkit-box-shadow .15s ease-in-out; transition: border-color .15s ease-in-out, -webkit-box-shadow .15s ease-in-out; -o-transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out; transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out; transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out, -webkit-box-shadow .15s ease-in-out; -webkit-border-radius: 0.5rem; -moz-border-radius: 0.5rem; -ms-border-radius: 0.5rem; -o-border-radius: 0.5rem; }
.invalid-feedback { display: none;width: 100%;margin-top: 0.2rem;font-size: 0.9rem;color: #dc3545; top: 4.2rem; left: 0; position: absolute; z-index: 0; }
.form-control.is-invalid, .was-validated .form-control:invalid { border-color: #dc3545; }
.is-invalid~.invalid-feedback,
.is-invalid~.invalid-tooltip,
.was-validated :invalid~.invalid-feedback,
.was-validated :invalid~.invalid-tooltip { display: block; }

@media (min-width: 768px) {
    .cc-countdown-form { margin-top: 4rem; }
    .form-group { margin-bottom: 2rem; }
    .form-label { margin-bottom: 0.5rem; font-size: 1.8rem; }
    .form-control { height: 5.2rem; padding: 1.5rem 1.8rem; font-size: 1.4rem; }
    .invalid-feedback {font-size: 1.2rem; top: 5.2rem; }
}

@media (min-width: 1920px) {
    .cc-countdown-form { margin-top: 5rem; }
}

@media (min-width: 1920px) and (min-height: 1070px) {
    .cc-countdown-form { margin-top: 6rem; }
}

/*cookie banner*/
.evidon-banner { font-size: 1.6rem !important; }
.evidon-banner div:last-child { display: none; }
.evidon-banner #_evh-ric-c:hover { border: none!important; outline: none!important }
.evidon-banner-message { font-size: 16px !important; line-height: 22.5px !important; }
.evidon-banner-acceptbutton,
.evidon-banner-declinebutton { font-size: 1em !important; line-height: 1 !important; padding: 0.75em 1em !important; border-radius: 2em !important; font-weight: 600 !important; width: 152px !important; }
.evidon-banner-message a { color:#fff; }
.evidon-banner-declinebutton:hover,
.evidon-banner-optionbutton:hover { outline: none; -webkit-box-shadow: 0 0 0 2px #363636!important; box-shadow: 0 0 0 2px #363636!important; }
.evidon-banner-acceptbutton:hover { outline: none; -webkit-box-shadow: 0 0 0 2px #eee!important; box-shadow: 0 0 0 2px #eee!important; }

@media (min-width: 1920px) {
    .evidon-banner { font-size: 1.8rem !important; }
}

@media (max-width: 767px) {
    .evidon-banner-message { padding-left: 80px !important; }
    .evidon-banner-image { left: 20px !important; }
    .evidon-banner-acceptbutton, .evidon-banner-declinebutton { width: auto !important; display: inline-block !important; }
    .evidon-banner-declinebutton { margin-left: 80px !important; }
    .evidon-banner-acceptbutton { margin-left: 15px !important; }
    .evidon-banner-message a { color:#fff; }
    .evidon-banner-declinebutton:hover, .evidon-banner-optionbutton:hover { outline: none; -webkit-box-shadow: 0 0 0 2px #363636!important; box-shadow: 0 0 0 2px #363636!important; }
    .evidon-banner-acceptbutton:hover { outline: none; -webkit-box-shadow: 0 0 0 2px #eee!important; box-shadow: 0 0 0 2px #eee!important; }
}

@media (max-width: 335px) {
    .evidon-banner-message { padding-left: 70px !important; }
    .evidon-banner-image { left: 10px !important; }
    .evidon-banner-message { font-size: 14px !important; line-height: 20.5px !important;  }
    .evidon-banner-declinebutton { margin-left: 70px !important; }
    .evidon-banner-acceptbutton { margin-left: 5px !important; }
}


@media (max-width: 1279px) {
    .cc-drag-body { display: none !important; }
}

@media (min-width: 2700px) {
    html.html-countdown { font-size: 17px; }
}

@media (min-width: 3840px) {
    html.html-countdown { font-size: 20px; }
}


.ratio-9x16 { --bs-aspect-ratio: 177.7777777777778%; }
.cc-s2-mid.ratio-9x16 { max-width: 36rem; margin-left: auto; margin-right: auto; }