<?php

namespace App\Http\Controllers\Admin;

use DB;
use Session;
use App\Excel\TextsExport;
use App\Excel\TextsImport;
use App\Framework\src\Http\Models\Language;
use App\Framework\src\Http\Models\DataRevision;
use App\Framework\src\Http\Models\Log;
use App\Framework\src\Http\Models\Text;
use App\Http\Controllers\Controller;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\View\View;
use Maatwebsite\Excel\Facades\Excel;

class TextController extends Controller{

    public $image_keys = array();

    /**
     * TextController constructor.
     *
     * @param Request $request
     */
    public function __construct(Request $request) {

        $this->middleware('ipcheck');
        $this->middleware('logincheck:text-management');
    }

    /**
     * @param Request $request
     *
     * @return Application|Factory|View
     */
    public function appTextForm(Request $request, $module=''){
		
		//if there is multiple entry with key and language id than delete that records
		DB::statement("DELETE t2 FROM texts t1 INNER JOIN texts t2 WHERE t1.id < t2.id AND t1.`key` = t2.`key` AND t1.`language_id` = t2.`language_id`");
		Text::whereRaw("`key` is null OR `key` = ''")->delete();
		
		$default_language_id = fS('app_settings.default_lang_id', 1);
		$language_options = Language::all()->pluck('code', 'id')->all();
		
        //change language
		if($request->get('langId') && isset($language_options[$request->get('langId')]))  {
			Session::put('admin_text_lang_id', $request->get('langId'));
		}
		
		if(Session::get('admin_text_lang_id'))  {
			$current_language_id = Session::get('admin_text_lang_id');
		}	else  {
			Session::put('admin_text_lang_id', $default_language_id);
			$current_language_id = $default_language_id;
		}
		

        //Fiter texts by module
        $search_where = "1 = 1";

        $prefixArr = module_text_prefixes();
        $removed_tabs = [];

        if($module && isset($prefixArr[$module]))  {
            
            $textTabs = explode(",", $prefixArr[$module]);
            $search_where_arr = [];

            foreach($textTabs as $textTab)  {
                $search_val_e = DB::connection()->getPdo()->quote($textTab . '%');
                $search_where_arr[] = "`key` LIKE $search_val_e";
            }

            $search_where = "(".implode(" OR ", $search_where_arr).")";
        }
        else  {
            foreach($prefixArr as $module1 => $text_prefixes)  {

                $text_prefixes = explode(",", $text_prefixes);

                foreach($text_prefixes as $text_prefix)  {
                    $removed_tabs[] = trim($text_prefix);
                }
            }
        }

        $texts = Text::where('language_id', $current_language_id)->where('key', 'NOT LIKE', 'mail.%')->whereRaw($search_where)->pluck('value', 'key')->all();
		$textKeys = Text::selectRaw('DISTINCT(`key`) as textKey')->where('key', 'NOT LIKE', 'mail.%')->whereRaw($search_where)->orderBy("key", "ASC")->get();
		
        $textArr = array();
		$tabs = array();
        foreach($textKeys as $textKey){
			$name_parts = explode(".", $textKey->textKey);
			
            if(!in_array($name_parts[0], $tabs) && !in_array($name_parts[0], $removed_tabs))  {
				$tabs[] = $name_parts[0];
			}
			
            $textArr[$name_parts[0]][$name_parts[1]][$name_parts[2]] = isset($texts[$textKey->textKey]) ? $texts[$textKey->textKey] : '';
        }

        $other_language = $language_options;
        //unset($other_language[$current_language_id]);
        $allTexts = [];
        $data['is_many_languages'] = false;
        if(count($other_language) > 1)
        {
            foreach($other_language as $id => $code)
            {
                $codeArray = explode('_',$code);
                $allTexts[$codeArray[0]] = Text::where('language_id', $id)->where('key', 'NOT LIKE', 'mail.%')->whereRaw($search_where)->pluck('value', 'key')->all();
            }
            //pr($allTexts);die;
            $data['is_many_languages'] = true;
        }
        $data['allTexts'] = $allTexts;    
        
        $data['tabs'] = $tabs;
        $data['text_arr'] = $textArr;
		$data['language_options'] = $language_options;
		$data['current_language_id'] = $current_language_id;
		$data['text_form'] = true;
        $data['module'] = $module;
        $data['admin'] = session()->get('backend_user');

        $data['active_menu'] = 'app_texts';
        if($module)  {
            $data['active_menu'] = $module.'_text';
        }

        return view('admin.app_texts_form', $data);
    }

    /**
     * @param Request $request
     *
     * @return Application|Factory|View
     */
    public function appEmailForm(Request $request, $module=''){
		
		//if there is multiple entry with key and language id than delete that records
		DB::statement("DELETE t2 FROM texts t1 INNER JOIN texts t2 WHERE t1.id < t2.id AND t1.`key` = t2.`key` AND t1.`language_id` = t2.`language_id`");
		Text::whereRaw("`key` is null OR `key` = ''")->delete();
		
		$default_language_id = fS('app_settings.default_lang_id', 1);
		$language_options = Language::all()->pluck('code', 'id')->all();
		
        //change language
		if($request->get('langId') && isset($language_options[$request->get('langId')]))  {
			Session::put('admin_text_lang_id', $request->get('langId'));
		}
		
		if(Session::get('admin_text_lang_id'))  {
			$current_language_id = Session::get('admin_text_lang_id');
		}	else  {
			Session::put('admin_text_lang_id', $default_language_id);
			$current_language_id = $default_language_id;
		}
		

        //Fiter texts by module
        $search_where = "1 = 1";

        $prefixArr = module_text_prefixes();
        $removed_tabs = [];

        if($module && isset($prefixArr[$module]))  {
            
            $textTabs = explode(",", $prefixArr[$module]);
            $search_where_arr = [];

            foreach($textTabs as $textTab)  {
                $search_val_e = DB::connection()->getPdo()->quote($textTab . '%');
                $search_where_arr[] = "`key` LIKE $search_val_e";
            }

            $search_where = "(".implode(" OR ", $search_where_arr).")";
        }
        else  {
            foreach($prefixArr as $module1 => $text_prefixes)  {

                $text_prefixes = explode(",", $text_prefixes);

                foreach($text_prefixes as $text_prefix)  {
                    $removed_tabs[] = trim($text_prefix);
                }
            }
        }

        $texts = Text::where('language_id', $current_language_id)->where('key', 'LIKE', 'mail.%')->whereRaw($search_where)->pluck('value', 'key')->all();
		$textKeys = Text::selectRaw('DISTINCT(`key`) as textKey')->where('key', 'LIKE', 'mail.%')->whereRaw($search_where)->orderBy("key", "ASC")->get();
		
        $textArr = array();
		$tabs = array();
        foreach($textKeys as $textKey){
			$name_parts = explode(".", $textKey->textKey);
			
            if(!in_array($name_parts[0], $tabs) && !in_array($name_parts[0], $removed_tabs))  {
				$tabs[] = $name_parts[0];
			}
			
            $textArr[$name_parts[0]][$name_parts[1]][$name_parts[2]] = isset($texts[$textKey->textKey]) ? $texts[$textKey->textKey] : '';
        }

        $other_language = $language_options;
        //unset($other_language[$current_language_id]);

        $allTexts = [];
        $data['is_many_languages'] = count($other_language) > 1 ? true : false;
        
        foreach($other_language as $id => $code)  {

            $codeArray = explode('_',$code);
            $allTexts[$codeArray[0]] = Text::where('language_id', $id)->where('key', 'LIKE', 'mail.%')->whereRaw($search_where)->pluck('value', 'key')->all();
        }
        
        $data['allTexts'] = $allTexts;    
        
        $data['tabs'] = $tabs;
        $data['text_arr'] = $textArr;
		$data['language_options'] = $language_options;
		$data['current_language_id'] = $current_language_id;
		$data['text_form'] = true;
        $data['module'] = $module;
        $data['admin'] = session()->get('backend_user');

        $data['active_menu'] = 'app_emails';
        if($module)  {
            $data['active_menu'] = $module.'_text';
        }

        return view('admin.app_emails_form', $data);
    }


    /**
     * @param Request $request
     * @param string  $id
     *
     * @return RedirectResponse
     */
    public function ajaxSaveText(Request $request){
		
        $textkey 	= $request->get("label");
        $textVal   	= $request->get("value");
		$isImage   	= $request->get("is_image");
        $lang_code  = $request->get("lang_code");
		//$is_multiple   	= $request->get("is_multiple");
        
        if(strpos($textkey, "mail.") === false)
            $textVal = sanitizeHTMLStr($textVal);

        $language_ids = [Session::get('admin_text_lang_id')];

        if($lang_code)
        {
            $language_ids = [];
            $language_ids[] = getLanguageLocaleIds()[$lang_code];
            //pr($language_ids);die;
        }

        //For same image in multi-language site
        /*if(strpos($textkey, '_image') !== false)
            $language_ids = Language::all()->pluck('id')->all();*/
        
        $firstLoopLang = true;
        foreach($language_ids as $current_language_id)  {

            $text = Text::where('language_id', $current_language_id)->where('key', sanitizeStr($textkey))->first();
        
            if($firstLoopLang && $isImage)  {

                $firstLoopLang = false;
                
                /*
                if($is_multiple)
                {
                    $fieldValues = $textVal ? explode(",", $textVal) : [];
                    foreach($fieldValues as $fieldValue)
                    {
                        if($fieldValue)
                            moveFileToUploads($fieldValue, '', @$text->id);
                    }
                }
                else
                    moveFileToUploads($textVal, @$text->value, @$text->id);
                */
            }
            
            if(!$text)  {
    
                //create new entry
                $text = new Text();
                $text->language_id = sanitizeStr($current_language_id);
                $text->key = sanitizeStr($textkey);
                $text->value = $textVal;
            }
            else  {
    
                //if image delete previous one
                if($text->value != $textVal && in_array($textkey, $this->image_keys))
                    @File::delete(public_path('uploads') . DIRECTORY_SEPARATOR . $text->value);
    
                $text->value = $textVal;
            }
            
            $text->save();
        }
        
        DataRevision::incrementRevision();
        Log::addFromRequest($request, shortClassMethod(__METHOD__), ['key' => $textkey, 'value' => $textVal]);

        echo json_encode(array('success' => 1));
    }

	
	/**
     * @param Request $request
     *
     * @return RedirectResponse
     */
    public function ajaxDeleteText(Request $request){
		
		$current_language_id = Session::get('admin_text_lang_id');
		
        $textkey = sanitizeStr($request->get("label"));
		$is_section = sanitizeStr($request->get("section"));
		
		if($is_section)  {
            $search_val_e = DB::connection()->getPdo()->quote($textkey.'%');
            $search_where = "(`key` LIKE $search_val_e)";
			$text = Text::where('language_id', $current_language_id)->whereRaw($search_where)->delete();
		}	else	{
			$text = Text::where('key', $textkey)->where('language_id', $current_language_id)->delete();
		}
        
        DataRevision::incrementRevision();
        Log::addFromRequest($request, shortClassMethod(__METHOD__), ['key' => $textkey]);
		
        echo json_encode(array('success' => 1));
    }
	

    /**
     * @param Request $request
     *
     * @return RedirectResponse
     */
    public function importTexts(Request $request){

        // Validation
        $allowedFileExts = ['xlsx'];
        if(!isset($_FILES['texts_file']) || $_FILES['texts_file']['error'] != 0){
            die("Upload error!");
        }

        $fileExt = request()->texts_file->getClientOriginalExtension();
        if(!in_array($fileExt, $allowedFileExts)){
            die("'" . $fileExt . "' file extension is not supported!");
        }

        // Move upload to temporary save location
        $tmpStorageDir = storage_path("tmp") . DIRECTORY_SEPARATOR . "uploads" . DIRECTORY_SEPARATOR;
        $tmpUploadFile = time() . rand() . ".tmp." . $fileExt;
        request()->texts_file->move($tmpStorageDir, $tmpUploadFile);
        $tmpFileFullPath = $tmpStorageDir . $tmpUploadFile;

        //import retailers
        Excel::import(new TextsImport, $tmpFileFullPath);

        // Remove original un-sanitized file
        @File::delete($tmpFileFullPath);

        // Logging
        DataRevision::incrementRevision();
        Log::addFromRequest($request, shortClassMethod(__METHOD__), ["file" => @basename($tmpFileFullPath)]);

        // Return
        return response()->json([
            "success" => 1,
        ]);
    }

    /**
     * @param Request $request
     *
     * @return RedirectResponse
     */
    public function exportTexts(Request $request, $module=''){

        Log::addFromRequest($request, shortClassMethod(__METHOD__));

        $fileName = $module ? $module."_texts.xlsx" : "texts.xlsx";
        return Excel::download(new TextsExport($module), $fileName);
    }

    /**
     * @param Request $request
     *
     * @return RedirectResponse
     */
    public function sendPreviewEmail(Request $request, $module=''){

        $email = sanitizeStr($request->input("email"));
        $mail_name = sanitizeStr($request->input("mail_name"));

        $admin = session()->get('backend_user');

        try{
            $mail_data = array(
                "force_to_email" => $email,
                "email"         => $admin->email,
                "username"      => $admin->username,
                "first_name"    => "John",
                "last_name"     => "Doe",
                "draw_date"     => "2025-05-28",
                "draw_errors"   => "Main Winner Error : All winner for main prize type is drawn!<br>
Follow Up 1 Winner Error : All winner for follow_up prize type is drawn!<br>
Follow Up 2 Winner Error : There are no new participants to draw as winners!<br>
Follow Up 3 Winner Error : There are no new participants to draw as winners!",
                "main_winner_link"     => "https://ps.playstation.com/days-of-play-2025/stream/winner-screen/main/MOhDYklTHzXqCRlnceHP",
                "follow_up_1_5_winner_link"     => "https://ps.playstation.com/days-of-play-2025/stream/winner-screen/follow_up_1-5/MOhDYklTHzXqCRlnceHP",
                "follow_up_6_10_winner_link"     => "https://ps.playstation.com/days-of-play-2025/stream/winner-screen/follow_up_6-10/MOhDYklTHzXqCRlnceHP",
            );

            sendSMTPEmail($mail_name, $mail_data);
        }    
        catch(TransportException $e)  {
            $error_msg = "Email sending failed!<br>Error - ".$e->getMessage();
            return [ 'success' => false , 'error' => $error_msg];
        }

        return ['success' => true];
    }
}