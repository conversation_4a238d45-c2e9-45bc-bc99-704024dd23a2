<?php
namespace App\Http\Controllers\Site;

use App\Http\Controllers\Controller;
use App\Http\Models\Participant;
use App\Http\Models\PsnUser;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\View\View;
use Psr\SimpleCache\InvalidArgumentException;

class DebugController extends Controller{
    /**
     * DebugController constructor.
     *
     * @param Request $request
     */
    public function __construct(Request $request){

        //$this->middleware('ipcheck');
    }

    /**
     * @param Request $request
     *
     * @return Application|Factory|View
     */
    public function index(Request $request){
        $serverName = $_SERVER["SERVER_NAME"] ?? "";
        if(!fS("app.allow_debug_login", 1)) return \response("Not allowed on live cloud installation!");
        if(!isDebugEnabled($request->ip())) return \response("Not allowed on live cloud installation!");

        session()->forget("debug_mode");
        return view('site.debug.debug');
    }

    /**
     * @param Request $request
     *
     * @return Application|ResponseFactory|RedirectResponse|Response
     */
    public function debugAction(Request $request){
        $serverName = $_SERVER["SERVER_NAME"] ?? "";
        if(!fS("app.allow_debug_login", 1)) return \response("Not allowed on live cloud installation!");
        if(!isDebugEnabled($request->ip())) return \response("Not allowed on live cloud installation!");

        $action = $request->input("action"); // clear_all
        if(!in_array($action, ["clear_all"])){
            redirect()->route("debug_index");
        }

        if($action == "clear_all"){

            Participant::query()->delete();
            return response("Participants deleted!");
        }
        return redirect()->route("debug_index");
    }

    /**
     * @param Request $request
     *
     * @return RedirectResponse
     * @throws InvalidArgumentException
     */
    public function debugLogin(Request $request){
        $serverName = $_SERVER["SERVER_NAME"] ?? "";
        if(!fS("app.allow_debug_login", 1)) return \response("Not allowed on live cloud installation!");
        if(!isDebugEnabled($request->ip())) return \response("Not allowed on live cloud installation!");
 
        // Sanitize user input
        $psnId     = sanitizeStrFull($request->input("psn_id"));
        $fakeDate  = sanitizeStrFull($request->input("fake_date"));
        $lastLogin = sanitizeStrFull($request->input("last_login"));
        $country_code = sanitizeStrFull($request->input("country_code"));
        $language_code = sanitizeStrFull($request->input("language_code"));
        $ps_plus_status = $request->input("ps_plus_status") ? sanitizeStrFull($request->input("ps_plus_status")) : "";

        $trophy_level   = @intval($request->input("trophy_level"));
        $bronze   = @intval($request->input("bronze"));
        $silver   = @intval($request->input("silver"));
        $gold     = @intval($request->input("gold"));
        $platinum = @intval($request->input("platinum"));

        if(!$psnId) return redirect()->route("debug_index");

        $psnUser = PsnUser::where([
            "online_id" => $psnId,
            "fake_user" => 1,
        ])->first();

        $currentTime = date("Y-m-d", @strtotime($lastLogin)) . " " . date("H:i:s");

        if(!$psnUser){

            $psnUser = new PsnUser();
            //$psnUser->first_login = $currentTime;
            $psnUser->fake_user = 1;
            $psnUser->bronze   = 0;
            $psnUser->silver   = 0;
            $psnUser->gold     = 0;
            $psnUser->platinum = 0;
        }

        //if($psnUser->ref_code == "")
            //$psnUser->ref_code = app(PsnApiController::class)->generateUniqueRefCodeFast($psnUser);
        
        //$psnUser->logins        = $psnUser->logins ? ($psnUser->logins + 1) : 1;
        //$psnUser->last_login    = $currentTime;
        $psnUser->online_id     = $psnId;
        $psnUser->user_uuid     = $psnId;
        $psnUser->country_code  = $country_code;
        $psnUser->language_code = $language_code;
        $psnUser->avtar_url     = "";
        $psnUser->save();

        cache()->store("array")->put("tmp_user_trophies", [
            "trophy_level"   => $trophy_level,
            "bronze"   => $bronze,
            "silver"   => $silver,
            "gold"     => $gold,
            "platinum" => $platinum,
        ]);

        $psnUserSess = [
            "id"           => $psnUser->id,
            "onlineId"     => $psnUser->online_id,
            "userUuid"     => $psnUser->user_uuid,
            "countryCode"  => $psnUser->country_code,
            "languageCode" => $psnUser->language_code,
            "userId"       => $psnId,
            "dcimId"       => "",
            "psPlusStatus" => $ps_plus_status,
            "isSubAccount" => "",
            "isFakeUser"   => "Y",
        ];

        session()->put("debug_mode", date("Y-m-d", @strtotime($fakeDate)));
        session()->forget("debug_mode_sec");

        //check if site is started for registration
        $site_live_time = strtotime(competitionStartDate()." 00:00:00");

        if(current_time() >= $site_live_time)  {

            $psnUserSess['last_login_ip'] = random_string(20);

            app(PsnApiController::class)->savePsnUserData($psnUserSess);

            return redirect()->route("site_page", ['page' => 'dashboard']);
        }
        else  {
            return redirect(route('site_home'));
        }
    }

    
    public function dummyParticipantsData(Request $request){

        /*
        for($ui=1; $ui <= 1000; $ui++)  {

            $online_id = random_string(rand(10, 15), true);

            $psnUser = PsnUser::selectRaw("*,".AESdecypt('email'))->where("online_id", $online_id)->first();
            if(!$psnUser)  {

                $psnUser = new PsnUser();
                $psnUser->online_id = $online_id;
                $psnUser->user_uuid = "uuid-".$online_id;
                $psnUser->user_id = $online_id;
                $psnUser->country_code = "DE";
                $psnUser->language_code = "de";
                $psnUser->save();

                $participant = new Participant();
                $participant->user_id = $psnUser->id;
                $participant->day = "2024-11-25";
                $participant->tickets = 1;
                $participant->tickets = 1;
                $participant->save();
            }
        }
        
        return "Dummy participants data added for 25.11";
        */

        return "";
    }
}
