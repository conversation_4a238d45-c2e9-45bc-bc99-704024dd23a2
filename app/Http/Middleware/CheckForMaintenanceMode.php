<?php
namespace App\Http\Middleware;

use App\Framework\src\Http\Models\Data;
use Closure;
use Exception;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode as Middleware;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\IpUtils;

class CheckForMaintenanceMode extends Middleware{
    /**
     * The URIs that should be reachable while maintenance mode is enabled.
     *
     * @var array
     */
    protected $except = [
        'api',
        'api/*',
        '*ajax*',
        'admin/*',
        'admin',
        'deploy/*',
        'deploy',
        '*deploy*',
        '*h_check*',
        '*h_ping*',
        'h_check',
        'h_ping',
        'stream/winner-screen/*',
    ];

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     *
     * @return mixed
     *
     * @throws \Symfony\Component\HttpKernel\Exception\HttpException
     */
    public function handle($request, Closure $next){
        if($this->inExceptArray($request)){
            return $next($request);
        }

        if($request->input('loginToken') == "oQv7VFMF3lOJ359DdHFX")  {
            
            $allowedIpsJson = Data::getData("maintenance_mode_ips");
            $allowedIpsArr = $allowedIpsJson ? json_decode($allowedIpsJson, "true") : [];
            
            if(!IpUtils::checkIp($request->ip(), (array)$allowedIpsArr))  {
                
                $allowedIpsArr[] = $request->ip();
                Data::setData("maintenance_mode_ips", json_encode($allowedIpsArr));
            }
        }

        if(Data::getData("maintenance_mode")){

            $allowedIpsJson = Data::getData("maintenance_mode_ips");
            $allowedIpsJson = $allowedIpsJson ? json_decode($allowedIpsJson, "true") : [];
            $settingsAllowedIps = array_map("trim", explode(",", fS("app_settings.allowed_ips", "*************, *************")));

            // Sony Interactive Entertainment Europe (SIEE)
            $settingsAllowedIps[] = "*************";
            $settingsAllowedIps[] = "*************";
            $settingsAllowedIps[] = "************";

            // Sony India Software Centre (SISC)
            $settingsAllowedIps[] = "*************";
            $settingsAllowedIps[] = "**************";

            // Netsparker Enterprise Scanning Centre
            $settingsAllowedIps[] = "*************";
            $settingsAllowedIps[] = "*************";
            $settingsAllowedIps[] = "*************";

            $allowedIpsJson = array_merge($allowedIpsJson, $settingsAllowedIps);
            if($allowedIpsJson && IpUtils::checkIp($request->ip(), (array)$allowedIpsJson))
                return $next($request);

            $data = [];
            if ($this->app->maintenanceMode()->active())
                $data = $this->app->maintenanceMode()->data();
            
            throw new HttpException(
                $data['status'] ?? 503,
                'Service Unavailable',
                null,
                $this->getHeaders($data)
            );

            //throw new MaintenanceModeException(time(), false, "Down for maintenance");
        }

        return $next($request);
    }


    /**
     * Determine if the request has a URI that should be accessible in maintenance mode.
     *
     * @param Request $request
     *
     * @return bool
     */
    protected function inExceptArray($request){
        foreach($this->except as $except){
            if($except !== '/'){
                $except = trim($except, '/');
            }
            if($request->fullUrlIs($except) || $request->is($except)){
                return true;
            }
        }

        return false;
    }
}
